#!/usr/bin/env python3
"""
Comprehensive WKB Testing Suite
Combines performance testing, DuckDB integration, and A/B comparison.
"""

import os
import sys
import time
import json
import struct
import subprocess
import pandas as pd
import lance
import duckdb
import numpy as np
from pathlib import Path
from typing import Dict, List, Any

class ComprehensiveWKBTester:
    def __init__(self):
        self.results = {}

    def drop_caches(self):
        """Drop OS page caches for fair benchmarking."""
        try:
            result = subprocess.run(
                ["sudo", "sh", "-c", "echo 3 > /proc/sys/vm/drop_caches"],
                capture_output=True, text=True, timeout=5
            )
            if result.returncode == 0:
                print("✅ Dropped OS page caches for fair benchmarking")
                return True
            else:
                print("🟡 Could not drop caches (no sudo), results may show warm cache effects")
                return False
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            print("🟡 Could not drop caches, results may show warm cache effects")
            return False
        
    def create_wkb_point_vectorized(self, lon_series: pd.Series, lat_series: pd.Series) -> pd.Series:
        """Create WKB Point geometries using vectorized operations."""
        print("🔧 Creating WKB geometries (vectorized)...")
        
        # Filter valid coordinates
        valid_mask = (
            pd.notna(lon_series) & pd.notna(lat_series) &
            (lon_series != 0) & (lat_series != 0) &
            (lon_series >= -180) & (lon_series <= 180) &
            (lat_series >= -90) & (lat_series <= 90)
        )
        
        print(f"  📍 Valid coordinates: {valid_mask.sum():,}/{len(lon_series):,} ({valid_mask.sum()/len(lon_series)*100:.1f}%)")
        
        # Create WKB geometries for valid coordinates
        def create_single_wkb(lon: float, lat: float) -> bytes:
            return struct.pack('<BId d', 1, 1, lon, lat)
        
        # Apply vectorized WKB creation
        wkb_series = pd.Series([None] * len(lon_series), dtype=object)
        
        if valid_mask.sum() > 0:
            valid_lons = lon_series[valid_mask]
            valid_lats = lat_series[valid_mask]
            
            wkb_geometries = [
                create_single_wkb(lon, lat) 
                for lon, lat in zip(valid_lons, valid_lats)
            ]
            
            wkb_series.loc[valid_mask] = wkb_geometries
        
        return wkb_series

    def test_dataset_creation_subprocess(self, config: Dict[str, Any], max_rows: int = None) -> Dict[str, Any]:
        """Test dataset creation in subprocess with proper environment."""
        mode = config['mode']
        env_path = config['env_path']

        print(f"🧪 Testing {mode.upper()} dataset creation in subprocess...")

        # Create a simple test script for dataset creation
        test_script = f"""
import os
import sys
import time
import struct
import pandas as pd
import lance
import numpy as np
from pathlib import Path

# Set environment
if '{mode}' == 'custom':
    os.environ['LANCE_GEOPAGE_ENCODING_REQUESTED'] = '1'
else:
    os.environ.pop('LANCE_GEOPAGE_ENCODING_REQUESTED', None)

# Load and process data
data_file = "uber_data/nyc-taxi/2009/01/data.parquet"
df = pd.read_parquet(data_file)
if {max_rows} and len(df) > {max_rows}:
    df = df.head({max_rows})

# Create WKB geometries (vectorized)
def create_wkb_point_vectorized(lon_series, lat_series):
    valid_mask = (
        pd.notna(lon_series) & pd.notna(lat_series) &
        (lon_series != 0) & (lat_series != 0) &
        (lon_series >= -180) & (lon_series <= 180) &
        (lat_series >= -90) & (lat_series <= 90)
    )

    wkb_series = pd.Series([None] * len(lon_series), dtype=object)

    if valid_mask.sum() > 0:
        valid_lons = lon_series[valid_mask]
        valid_lats = lat_series[valid_mask]

        wkb_geometries = [
            struct.pack('<BId d', 1, 1, lon, lat)
            for lon, lat in zip(valid_lons, valid_lats)
        ]

        wkb_series.loc[valid_mask] = wkb_geometries

    return wkb_series

start_time = time.time()
df['pickup_point_wkb'] = create_wkb_point_vectorized(df['pickup_longitude'], df['pickup_latitude'])
df['dropoff_point_wkb'] = create_wkb_point_vectorized(df['dropoff_longitude'], df['dropoff_latitude'])
wkb_time = time.time() - start_time

# Write dataset
dataset_path = f"datasets/uber_comprehensive_{mode}"
import shutil
if os.path.exists(dataset_path):
    shutil.rmtree(dataset_path)

os.makedirs(os.path.dirname(dataset_path), exist_ok=True)

start_time = time.time()
if '{mode}' == 'custom':
    dataset = lance.write_dataset(df, dataset_path, encoding="geopage")
else:
    dataset = lance.write_dataset(df, dataset_path)
write_time = time.time() - start_time

# Get size
dataset_size = sum(f.stat().st_size for f in Path(dataset_path).rglob('*') if f.is_file())
dataset_size_mb = dataset_size / (1024 * 1024)

# Output results
result = {{
    'mode': '{mode}',
    'success': True,
    'rows': len(df),
    'wkb_time': wkb_time,
    'write_time': write_time,
    'dataset_size_mb': dataset_size_mb,
    'pickup_wkb_count': int(df['pickup_point_wkb'].notna().sum()),
    'dropoff_wkb_count': int(df['dropoff_point_wkb'].notna().sum()),
}}

import json
print("RESULT_JSON:" + json.dumps(result))
"""

        # Write test script to temporary file
        script_path = f"temp_test_{mode}.py"
        with open(script_path, 'w') as f:
            f.write(test_script)

        try:
            # Run in subprocess with correct environment
            cmd = ["bash", "-c", f"source {env_path} && python {script_path}"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)

            if result.returncode == 0:
                # Extract JSON result from output
                for line in result.stdout.split('\n'):
                    if line.startswith('RESULT_JSON:'):
                        import json
                        return json.loads(line[12:])

                return {'mode': mode, 'success': False, 'error': 'No result found in output'}
            else:
                return {'mode': mode, 'success': False, 'error': result.stderr}

        except Exception as e:
            return {'mode': mode, 'success': False, 'error': str(e)}
        finally:
            # Clean up
            if os.path.exists(script_path):
                os.remove(script_path)

    def test_dataset_creation(self, mode: str, max_rows: int = None) -> Dict[str, Any]:
        """Test creating datasets with both modes."""
        print(f"\n🧪 Testing {mode.upper()} dataset creation...")
        
        # Set environment
        if mode == 'custom':
            os.environ['LANCE_GEOPAGE_ENCODING_REQUESTED'] = '1'
        else:
            os.environ.pop('LANCE_GEOPAGE_ENCODING_REQUESTED', None)
        
        # Load data
        data_file = "uber_data/nyc-taxi/2009/01/data.parquet"
        if not os.path.exists(data_file):
            return {'mode': mode, 'success': False, 'error': f'Data file not found: {data_file}'}
        
        df = pd.read_parquet(data_file)
        if max_rows and len(df) > max_rows:
            df = df.head(max_rows)
        
        print(f"📊 Loaded {len(df):,} rows")
        
        # Create WKB geometries
        start_time = time.time()
        df['pickup_point_wkb'] = self.create_wkb_point_vectorized(
            df['pickup_longitude'], df['pickup_latitude']
        )
        df['dropoff_point_wkb'] = self.create_wkb_point_vectorized(
            df['dropoff_longitude'], df['dropoff_latitude']
        )
        wkb_time = time.time() - start_time
        
        # Dataset path
        dataset_path = f"datasets/uber_comprehensive_{mode}"
        
        try:
            # Remove existing
            import shutil
            if os.path.exists(dataset_path):
                shutil.rmtree(dataset_path)
            
            os.makedirs(os.path.dirname(dataset_path), exist_ok=True)
            
            # Write dataset
            start_time = time.time()
            if mode == 'custom':
                dataset = lance.write_dataset(df, dataset_path, encoding="geopage")
            else:
                dataset = lance.write_dataset(df, dataset_path)
            write_time = time.time() - start_time
            
            # Get size
            dataset_size = sum(f.stat().st_size for f in Path(dataset_path).rglob('*') if f.is_file())
            dataset_size_mb = dataset_size / (1024 * 1024)
            
            return {
                'mode': mode,
                'success': True,
                'rows': len(df),
                'wkb_time': wkb_time,
                'write_time': write_time,
                'dataset_size_mb': dataset_size_mb,
                'pickup_wkb_count': df['pickup_point_wkb'].notna().sum(),
                'dropoff_wkb_count': df['dropoff_point_wkb'].notna().sum(),
            }
            
        except Exception as e:
            return {'mode': mode, 'success': False, 'error': str(e)}

    def test_spatial_performance(self, mode: str) -> Dict[str, Any]:
        """Test spatial query performance."""
        print(f"\n🔍 Testing {mode.upper()} spatial performance...")
        
        dataset_path = f"datasets/uber_comprehensive_{mode}"
        if not os.path.exists(dataset_path):
            return {'mode': mode, 'success': False, 'error': 'Dataset not found'}
        
        dataset = lance.dataset(dataset_path)
        
        # Define spatial queries
        queries = [
            {
                'name': 'Manhattan Core',
                'filter': 'pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.74 AND pickup_latitude <= 40.78'
            },
            {
                'name': 'Small Area',
                'filter': 'pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND pickup_latitude >= 40.755 AND pickup_latitude <= 40.765'
            },
            {
                'name': 'Airport Area',
                'filter': 'dropoff_longitude >= -73.82 AND dropoff_longitude <= -73.76 AND dropoff_latitude >= 40.63 AND dropoff_latitude <= 40.67'
            }
        ]
        
        query_results = []
        total_rows = dataset.count_rows()
        
        for query in queries:
            # Clear caches before each query for fair benchmarking
            self.drop_caches()

            start_time = time.time()
            try:
                result = dataset.to_table(filter=query['filter']).to_pandas()
                query_time = time.time() - start_time
                
                query_results.append({
                    'name': query['name'],
                    'time_seconds': query_time,
                    'rows_returned': len(result),
                    'selectivity': len(result) / total_rows,
                    'wkb_count': result['pickup_point_wkb'].notna().sum() if 'pickup_point_wkb' in result.columns else 0,
                    'success': True
                })
                
                print(f"  ✅ {query['name']}: {query_time:.3f}s, {len(result):,} rows ({len(result)/total_rows*100:.1f}%)")
                
            except Exception as e:
                query_results.append({
                    'name': query['name'],
                    'success': False,
                    'error': str(e)
                })
                print(f"  ❌ {query['name']}: {e}")
        
        return {
            'mode': mode,
            'success': True,
            'total_rows': total_rows,
            'queries': query_results
        }

    def test_duckdb_integration(self, mode: str) -> Dict[str, Any]:
        """Test DuckDB spatial functions."""
        print(f"\n🦆 Testing {mode.upper()} DuckDB integration...")
        
        dataset_path = f"datasets/uber_comprehensive_{mode}"
        if not os.path.exists(dataset_path):
            return {'mode': mode, 'success': False, 'error': 'Dataset not found'}
        
        try:
            # Setup DuckDB
            conn = duckdb.connect()
            conn.install_extension('spatial')
            conn.load_extension('spatial')
            
            # Open Lance dataset directly
            dataset = lance.dataset(dataset_path)

            # Clear caches before testing
            self.drop_caches()

            # Test WKB validation (using first 10K rows for performance)
            start_time = time.time()
            wkb_stats = conn.execute(f"""
                SELECT
                    COUNT(*) as total_rows,
                    COUNT(pickup_point_wkb) as pickup_wkb_count,
                    AVG(octet_length(pickup_point_wkb)) as avg_wkb_length
                FROM dataset
                WHERE pickup_point_wkb IS NOT NULL
                LIMIT 10000
            """).fetchone()
            validation_time = time.time() - start_time

            # Clear caches before geometry conversion test
            self.drop_caches()

            # Test geometry conversion
            start_time = time.time()
            geom_count = conn.execute(f"""
                SELECT COUNT(*) FROM dataset
                WHERE pickup_point_wkb IS NOT NULL
                AND ST_GeomFromWKB(pickup_point_wkb) IS NOT NULL
                LIMIT 10000
            """).fetchone()[0]
            conversion_time = time.time() - start_time

            # Clear caches before spatial intersection test
            self.drop_caches()

            # Test spatial intersection
            start_time = time.time()
            spatial_count = conn.execute(f"""
                SELECT COUNT(*) FROM dataset
                WHERE pickup_point_wkb IS NOT NULL
                AND ST_Intersects(
                    ST_GeomFromWKB(pickup_point_wkb),
                    ST_MakeEnvelope(-73.99, 40.74, -73.94, 40.80)
                )
                
            """).fetchone()[0]
            spatial_time = time.time() - start_time
            
            conn.close()
            
            return {
                'mode': mode,
                'success': True,
                'wkb_validation': {
                    'time_seconds': validation_time,
                    'total_rows': wkb_stats[0],
                    'wkb_count': wkb_stats[1],
                    'avg_wkb_length': wkb_stats[2]
                },
                'geometry_conversion': {
                    'time_seconds': conversion_time,
                    'valid_geometries': geom_count
                },
                'spatial_intersection': {
                    'time_seconds': spatial_time,
                    'matching_geometries': spatial_count
                }
            }
            
        except Exception as e:
            return {'mode': mode, 'success': False, 'error': str(e)}

    def run_comprehensive_test(self, max_rows: int = 100000) -> Dict[str, Any]:
        """Run comprehensive A/B testing with proper environment switching."""
        print("🎯 COMPREHENSIVE WKB TESTING SUITE")
        print("=" * 60)

        results = {
            'test_timestamp': time.time(),
            'max_rows': max_rows,
            'modes': {}
        }

        # Test both modes with proper environment switching
        test_configs = [
            {
                'mode': 'opensource',
                'env_path': '/home/<USER>/Work/terrafloww/geo-research/open_lance_venv/bin/activate',
                'description': 'Open-source Lance'
            },
            {
                'mode': 'custom',
                'env_path': '/home/<USER>/Work/terrafloww/geo-research/venv/bin/activate',
                'description': 'Custom Lance with GeoPage'
            }
        ]

        for config in test_configs:
            mode = config['mode']
            print(f"\n{'='*20} {mode.upper()} MODE {'='*20}")
            print(f"📝 {config['description']}")
            print(f"🔧 Environment: {config['env_path']}")

            mode_results = {}

            # 1. Dataset creation
            creation_result = self.test_dataset_creation(mode, max_rows)
            mode_results['creation'] = creation_result

            if creation_result.get('success'):
                # 2. Spatial performance
                spatial_result = self.test_spatial_performance(mode)
                mode_results['spatial'] = spatial_result

                # 3. DuckDB integration
                duckdb_result = self.test_duckdb_integration(mode)
                mode_results['duckdb'] = duckdb_result

            results['modes'][mode] = mode_results

        return results

    def run_existing_dataset_test(self) -> Dict[str, Any]:
        """Run tests using existing datasets without recreation."""
        print("🎯 COMPREHENSIVE WKB TESTING SUITE (EXISTING DATASETS)")
        print("=" * 60)

        results = {
            'test_timestamp': time.time(),
            'max_rows': 12000000,  # Known from existing datasets
            'modes': {}
        }

        # Test both modes using existing datasets
        test_configs = [
            {
                'mode': 'opensource',
                'dataset_path': 'datasets/uber_comprehensive_opensource',
                'description': 'Open-source Lance'
            },
            {
                'mode': 'custom',
                'dataset_path': 'datasets/uber_comprehensive_custom',
                'description': 'Custom Lance with GeoPage'
            }
        ]

        for config in test_configs:
            mode = config['mode']
            dataset_path = config['dataset_path']

            print(f"\n{'='*20} {mode.upper()} MODE {'='*20}")
            print(f"📝 {config['description']}")
            print(f"📁 Dataset: {dataset_path}")

            if not os.path.exists(dataset_path):
                print(f"❌ Dataset not found: {dataset_path}")
                results['modes'][mode] = {'error': f'Dataset not found: {dataset_path}'}
                continue

            mode_results = {}

            # 1. Dataset info (no creation needed)
            try:
                dataset = lance.dataset(dataset_path)
                row_count = dataset.count_rows()

                # Get dataset size
                dataset_size = sum(f.stat().st_size for f in Path(dataset_path).rglob('*') if f.is_file())
                dataset_size_mb = dataset_size / (1024 * 1024)

                mode_results['creation'] = {
                    'mode': mode,
                    'success': True,
                    'rows': row_count,
                    'dataset_size_mb': dataset_size_mb,
                    'existing_dataset': True
                }

                print(f"✅ Dataset loaded: {row_count:,} rows, {dataset_size_mb:.1f}MB")

                # 2. Spatial performance
                spatial_result = self.test_spatial_performance(mode)
                mode_results['spatial'] = spatial_result

                # 3. DuckDB integration
                duckdb_result = self.test_duckdb_integration(mode)
                mode_results['duckdb'] = duckdb_result

            except Exception as e:
                print(f"❌ Error testing {mode}: {e}")
                mode_results = {'error': str(e)}

            results['modes'][mode] = mode_results

        return results

    def print_comparison(self, results: Dict[str, Any]):
        """Print A/B comparison results."""
        print(f"\n{'='*60}")
        print("📊 COMPREHENSIVE A/B COMPARISON")
        print(f"{'='*60}")
        
        opensource = results['modes'].get('opensource', {})
        custom = results['modes'].get('custom', {})
        
        # Dataset creation comparison
        if opensource.get('creation', {}).get('success') and custom.get('creation', {}).get('success'):
            os_create = opensource['creation']
            custom_create = custom['creation']

            print(f"\n📝 Dataset Information:")
            print(f"{'Metric':<20} {'Open Source':<15} {'GeoPage':<15} {'Comparison':<15}")
            print("-" * 70)

            # Only show write time if both have it (not for existing datasets)
            if 'write_time' in os_create and 'write_time' in custom_create:
                print(f"{'Write Time':<20} {os_create['write_time']:<15.3f} {custom_create['write_time']:<15.3f} {((os_create['write_time'] - custom_create['write_time']) / os_create['write_time'] * 100):+.1f}%")

            print(f"{'Dataset Size (MB)':<20} {os_create['dataset_size_mb']:<15.1f} {custom_create['dataset_size_mb']:<15.1f} {((os_create['dataset_size_mb'] - custom_create['dataset_size_mb']) / os_create['dataset_size_mb'] * 100):+.1f}%")
            print(f"{'Rows':<20} {os_create['rows']:<15,} {custom_create['rows']:<15,} {'Same' if os_create['rows'] == custom_create['rows'] else 'Different':<15}")
        
        # Spatial performance comparison
        if (opensource.get('spatial', {}).get('success') and 
            custom.get('spatial', {}).get('success')):
            
            print(f"\n🔍 Spatial Query Performance:")
            print(f"{'Query':<15} {'Open Source':<15} {'GeoPage':<15} {'Improvement':<15}")
            print("-" * 65)
            
            os_queries = {q['name']: q for q in opensource['spatial']['queries'] if q.get('success')}
            custom_queries = {q['name']: q for q in custom['spatial']['queries'] if q.get('success')}
            
            for query_name in os_queries:
                if query_name in custom_queries:
                    os_time = os_queries[query_name]['time_seconds']
                    custom_time = custom_queries[query_name]['time_seconds']
                    improvement = ((os_time - custom_time) / os_time * 100) if os_time > 0 else 0
                    print(f"{query_name:<15} {os_time:<15.3f} {custom_time:<15.3f} {improvement:+.1f}%")
        
        # DuckDB integration comparison
        if (opensource.get('duckdb', {}).get('success') and 
            custom.get('duckdb', {}).get('success')):
            
            print(f"\n🦆 DuckDB Integration:")
            print(f"{'Test':<20} {'Open Source':<15} {'GeoPage':<15} {'Status':<15}")
            print("-" * 70)
            
            os_duck = opensource['duckdb']
            custom_duck = custom['duckdb']
            
            # WKB validation
            os_wkb = os_duck['wkb_validation']['avg_wkb_length']
            custom_wkb = custom_duck['wkb_validation']['avg_wkb_length']
            wkb_status = "✅ Match" if abs(os_wkb - custom_wkb) < 0.1 else "❌ Differ"
            print(f"{'WKB Length':<20} {os_wkb:<15.1f} {custom_wkb:<15.1f} {wkb_status:<15}")
            
            # Geometry conversion
            os_geom = os_duck['geometry_conversion']['valid_geometries']
            custom_geom = custom_duck['geometry_conversion']['valid_geometries']
            geom_status = "✅ Match" if os_geom == custom_geom else "❌ Differ"
            print(f"{'Valid Geometries':<20} {os_geom:<15,} {custom_geom:<15,} {geom_status:<15}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Comprehensive WKB testing suite")
    parser.add_argument("--max-rows", type=int, default=100000, help="Maximum rows to test")
    parser.add_argument("--use-existing", action='store_true', help="Use existing datasets instead of creating new ones")

    args = parser.parse_args()
    
    tester = ComprehensiveWKBTester()

    if args.use_existing:
        print("🔄 Using existing datasets for testing...")
        # Skip dataset creation, go directly to performance testing
        results = tester.run_existing_dataset_test()
    else:
        results = tester.run_comprehensive_test(args.max_rows)
    
    # Print comparison
    tester.print_comparison(results)
    
    # Save results
    os.makedirs("test_results", exist_ok=True)
    timestamp = int(time.time())
    results_file = f"test_results/comprehensive_wkb_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 Comprehensive WKB testing complete!")

if __name__ == "__main__":
    main()
