{"test_timestamp": 1749543563.8288798, "max_rows": 1000000, "modes": {"opensource": {"creation": {"mode": "opensource", "success": true, "rows": 1000000, "wkb_time": 0.4724845886230469, "write_time": 0.6129906177520752, "dataset_size_mb": 150.001482963562, "pickup_wkb_count": "984878", "dropoff_wkb_count": "985136"}, "spatial": {"mode": "opensource", "success": true, "total_rows": 1000000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.11354613304138184, "rows_returned": 302646, "selectivity": 0.302646, "wkb_count": "302646", "success": true}, {"name": "Small Area", "time_seconds": 0.034368038177490234, "rows_returned": 55369, "selectivity": 0.055369, "wkb_count": "55369", "success": true}, {"name": "Airport Area", "time_seconds": 0.029694795608520508, "rows_returned": 6061, "selectivity": 0.006061, "wkb_count": "6061", "success": true}]}, "duckdb": {"mode": "opensource", "success": true, "sample_size": 10000, "wkb_validation": {"time_seconds": 0.0024824142456054688, "total_rows": 10000, "wkb_count": 10000, "avg_wkb_length": 21.0}, "geometry_conversion": {"time_seconds": 0.0023877620697021484, "valid_geometries": 10000}, "spatial_intersection": {"time_seconds": 0.0031516551971435547, "matching_geometries": 5530}}}, "custom": {"creation": {"mode": "custom", "success": true, "rows": 1000000, "wkb_time": 0.45617222785949707, "write_time": 0.6325836181640625, "dataset_size_mb": 150.0014820098877, "pickup_wkb_count": "984878", "dropoff_wkb_count": "985136"}, "spatial": {"mode": "custom", "success": true, "total_rows": 1000000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.11478376388549805, "rows_returned": 302646, "selectivity": 0.302646, "wkb_count": "302646", "success": true}, {"name": "Small Area", "time_seconds": 0.03177142143249512, "rows_returned": 55369, "selectivity": 0.055369, "wkb_count": "55369", "success": true}, {"name": "Airport Area", "time_seconds": 0.022475481033325195, "rows_returned": 6061, "selectivity": 0.006061, "wkb_count": "6061", "success": true}]}, "duckdb": {"mode": "custom", "success": true, "sample_size": 10000, "wkb_validation": {"time_seconds": 0.002414703369140625, "total_rows": 10000, "wkb_count": 10000, "avg_wkb_length": 21.0}, "geometry_conversion": {"time_seconds": 0.0024518966674804688, "valid_geometries": 10000}, "spatial_intersection": {"time_seconds": 0.003137826919555664, "matching_geometries": 5530}}}}}