{"mode": "opensource", "data_info": {"rows": 4000000, "columns": 20, "pickup_wkb_count": "3939334", "dropoff_wkb_count": "3940378", "memory_mb": 1286.7178812026978}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 3939334, "validity_rate": 0.9848335, "min_lon": -85.50151062011719, "max_lon": 0.09812700003385544, "min_lat": -0.07020799815654755, "max_lat": 81.53500366210938}, "dropoff_bounds": {"valid_rows": 3940378, "validity_rate": 0.9850945, "min_lon": -83.43216705322266, "max_lon": 0.10140500217676163, "min_lat": -0.0662280023097992, "max_lat": 81.53500366210938}}, "write_performance": {"time_seconds": 0.0, "dataset_size_mb": 414.9764595031738, "compression_ratio": 3.100700899379225, "write_success": true, "existing_dataset": true}, "wkb_column_performance": {"time_seconds": 0.0, "pickup_wkb_count": 0, "dropoff_wkb_count": 0, "wkb_success": false, "skipped": false, "error": "External error: RuntimeError: Task was aborted"}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "error": "External error: RuntimeError: Task was aborted"}, {"name": "Small Area WKB", "error": "External error: RuntimeError: Task was aborted"}, {"name": "Airport Area WKB", "error": "External error: RuntimeError: Task was aborted"}]}