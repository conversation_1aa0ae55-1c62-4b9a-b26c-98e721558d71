{"test_timestamp": 1751193278.377167, "max_rows": 4000000, "modes": {"opensource": {"creation": {"mode": "opensource", "success": true, "rows": 4000000, "wkb_time": 1.8443167209625244, "write_time": 1.4986865520477295, "dataset_size_mb": 414.9764595031738, "pickup_wkb_count": "3939334", "dropoff_wkb_count": "3940378"}, "spatial": {"mode": "opensource", "success": true, "total_rows": 4000000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.24455022811889648, "rows_returned": 1208458, "selectivity": 0.3021145, "wkb_count": "1208458", "success": true}, {"name": "Small Area", "time_seconds": 0.15029501914978027, "rows_returned": 221711, "selectivity": 0.05542775, "wkb_count": "221711", "success": true}, {"name": "Airport Area", "time_seconds": 0.09955358505249023, "rows_returned": 24424, "selectivity": 0.006106, "wkb_count": "24424", "success": true}]}, "duckdb": {"mode": "opensource", "success": false, "error": "Invalid Input Error: Could not parse WKB input:Out of bounds read (is the WKB corrupt?)"}}, "custom": {"creation": {"mode": "custom", "success": true, "rows": 4000000, "wkb_time": 1.8165264129638672, "write_time": 1.525500774383545, "dataset_size_mb": 414.9764585494995, "pickup_wkb_count": "3939334", "dropoff_wkb_count": "3940378"}, "spatial": {"mode": "custom", "success": true, "total_rows": 4000000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.16538095474243164, "rows_returned": 1208458, "selectivity": 0.3021145, "wkb_count": "1208458", "success": true}, {"name": "Small Area", "time_seconds": 0.0990748405456543, "rows_returned": 221711, "selectivity": 0.05542775, "wkb_count": "221711", "success": true}, {"name": "Airport Area", "time_seconds": 0.07226276397705078, "rows_returned": 24424, "selectivity": 0.006106, "wkb_count": "24424", "success": true}]}, "duckdb": {"mode": "custom", "success": false, "error": "Invalid Input Error: Could not parse WKB input:Out of bounds read (is the WKB corrupt?)"}}}}