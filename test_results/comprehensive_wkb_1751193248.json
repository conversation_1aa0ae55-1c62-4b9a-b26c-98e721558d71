{"test_timestamp": 1751193236.9271338, "max_rows": 50000, "modes": {"opensource": {"creation": {"mode": "opensource", "success": true, "rows": 50000, "wkb_time": 0.02414417266845703, "write_time": 0.037627220153808594, "dataset_size_mb": 5.211770057678223, "pickup_wkb_count": "49355", "dropoff_wkb_count": "49377"}, "spatial": {"mode": "opensource", "success": true, "total_rows": 50000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.025246858596801758, "rows_returned": 15161, "selectivity": 0.30322, "wkb_count": "15161", "success": true}, {"name": "Small Area", "time_seconds": 0.013908624649047852, "rows_returned": 2781, "selectivity": 0.05562, "wkb_count": "2781", "success": true}, {"name": "Airport Area", "time_seconds": 0.016030073165893555, "rows_returned": 312, "selectivity": 0.00624, "wkb_count": "312", "success": true}]}, "duckdb": {"mode": "opensource", "success": false, "error": "Invalid Input Error: Could not parse WKB input:Out of bounds read (is the WKB corrupt?)"}}, "custom": {"creation": {"mode": "custom", "success": true, "rows": 50000, "wkb_time": 0.023259639739990234, "write_time": 0.0516963005065918, "dataset_size_mb": 5.211770057678223, "pickup_wkb_count": "49355", "dropoff_wkb_count": "49377"}, "spatial": {"mode": "custom", "success": true, "total_rows": 50000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.014506340026855469, "rows_returned": 15161, "selectivity": 0.30322, "wkb_count": "15161", "success": true}, {"name": "Small Area", "time_seconds": 0.011718988418579102, "rows_returned": 2781, "selectivity": 0.05562, "wkb_count": "2781", "success": true}, {"name": "Airport Area", "time_seconds": 0.01214456558227539, "rows_returned": 312, "selectivity": 0.00624, "wkb_count": "312", "success": true}]}, "duckdb": {"mode": "custom", "success": false, "error": "Invalid Input Error: Could not parse WKB input:Out of bounds read (is the WKB corrupt?)"}}}}