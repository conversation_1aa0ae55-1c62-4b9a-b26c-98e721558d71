{"mode": "custom", "data_info": {"rows": 5000000, "columns": 20, "pickup_wkb_count": "4943874", "dropoff_wkb_count": "4945425", "memory_mb": 1647.2954578399658}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 4943874, "validity_rate": 0.9887748, "min_lon": -107.33740234375, "max_lon": 98.68528747558594, "min_lat": -1.0473469495773315, "max_lat": 81.2884521484375}, "dropoff_bounds": {"valid_rows": 4945425, "validity_rate": 0.989085, "min_lon": -94.20185089111328, "max_lon": 98.68528747558594, "min_lat": -7.33510684967041, "max_lat": 81.56687927246094}}, "write_performance": {"time_seconds": 2.23305606842041, "dataset_size_mb": 520.7282657623291, "compression_ratio": 3.1634454400672474, "write_success": true, "existing_dataset": false}, "wkb_column_performance": {"time_seconds": 0.07715106010437012, "pickup_wkb_count": 5000000, "dropoff_wkb_count": 5000000, "wkb_success": true, "skipped": false}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "error": "External error: RuntimeError: Task was aborted"}, {"name": "Small Area WKB", "error": "External error: RuntimeError: Task was aborted"}, {"name": "Airport Area WKB", "error": "External error: RuntimeError: Task was aborted"}]}