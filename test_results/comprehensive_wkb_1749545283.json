{"test_timestamp": 1749545265.9295068, "max_rows": 7000000, "modes": {"opensource": {"creation": {"mode": "opensource", "success": true, "rows": 7000000, "wkb_time": 3.2159297466278076, "write_time": 2.6625678539276123, "dataset_size_mb": 1050.176549911499, "pickup_wkb_count": "6894314", "dropoff_wkb_count": "6896106"}, "spatial": {"mode": "opensource", "success": true, "total_rows": 7000000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.5912876129150391, "rows_returned": 2116361, "selectivity": 0.3023372857142857, "wkb_count": "2116361", "success": true}, {"name": "Small Area", "time_seconds": 0.2754993438720703, "rows_returned": 388798, "selectivity": 0.05554257142857143, "wkb_count": "388798", "success": true}, {"name": "Airport Area", "time_seconds": 0.10616254806518555, "rows_returned": 42578, "selectivity": 0.0060825714285714285, "wkb_count": "42578", "success": true}]}, "duckdb": {"mode": "opensource", "success": false, "error": "Invalid Input Error: arrow_scan: get_next failed(): IOError: Io error: External error: Encountered internal error. Please file a bug report at https://github.com/lancedb/lance/issues. Error decoding batch: Invalid user input: Row range exceeds available offsets, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/encodings/geopage.rs:1897:31, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/decoder.rs:2585:31"}}, "custom": {"creation": {"mode": "custom", "success": true, "rows": 7000000, "wkb_time": 3.2219581604003906, "write_time": 2.711791753768921, "dataset_size_mb": 1050.1765489578247, "pickup_wkb_count": "6894314", "dropoff_wkb_count": "6896106"}, "spatial": {"mode": "custom", "success": true, "total_rows": 7000000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.6004738807678223, "rows_returned": 2116361, "selectivity": 0.3023372857142857, "wkb_count": "2116361", "success": true}, {"name": "Small Area", "time_seconds": 0.275836706161499, "rows_returned": 388798, "selectivity": 0.05554257142857143, "wkb_count": "388798", "success": true}, {"name": "Airport Area", "time_seconds": 0.10706067085266113, "rows_returned": 42578, "selectivity": 0.0060825714285714285, "wkb_count": "42578", "success": true}]}, "duckdb": {"mode": "custom", "success": false, "error": "Invalid Input Error: arrow_scan: get_next failed(): IOError: Io error: External error: Encountered internal error. Please file a bug report at https://github.com/lancedb/lance/issues. Error decoding batch: Invalid user input: Row range exceeds available offsets, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/encodings/geopage.rs:1897:31, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/decoder.rs:2585:31"}}}}