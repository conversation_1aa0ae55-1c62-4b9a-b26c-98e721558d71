{"mode": "custom", "data_info": {"rows": 12000000, "columns": 20, "pickup_wkb_count": "11818356", "dropoff_wkb_count": "11821480", "memory_mb": 3860.1734285354614}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 11818356, "validity_rate": 0.984863, "min_lon": -94.10173034667969, "max_lon": 0.09812700003385544, "min_lat": -7.33510684967041, "max_lat": 81.53500366210938}, "dropoff_bounds": {"valid_rows": 11821480, "validity_rate": 0.9851233333333334, "min_lon": -94.32691192626953, "max_lon": 0.10140500217676163, "min_lat": -7.33510684967041, "max_lat": 81.53500366210938}}, "write_performance": {"time_seconds": 26.787853479385376, "dataset_size_mb": 1245.1747817993164, "compression_ratio": 3.1001056919554624, "write_success": true, "existing_dataset": false}, "wkb_column_performance": {"time_seconds": 0.3816370964050293, "pickup_wkb_count": 12000000, "dropoff_wkb_count": 12000000, "wkb_success": true, "skipped": false}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "description": "High-density Manhattan area for WKB testing", "filter": "pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.74 AND pickup_latitude <= 40.78", "time_seconds": 2.9260873794555664, "rows_returned": 3628392, "selectivity": 0.302366, "wkb_geometries_returned": "3628392"}, {"name": "Small Area WKB", "description": "Small selective area for WKB spatial optimization", "filter": "pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND pickup_latitude >= 40.755 AND pickup_latitude <= 40.765", "time_seconds": 1.6035668849945068, "rows_returned": 665771, "selectivity": 0.055480916666666664, "wkb_geometries_returned": "665771"}, {"name": "Airport Area WKB", "description": "JFK Airport vicinity for WKB testing", "filter": "dropoff_longitude >= -73.82 AND dropoff_longitude <= -73.76 AND dropoff_latitude >= 40.63 AND dropoff_latitude <= 40.67", "time_seconds": 1.2466967105865479, "rows_returned": 72838, "selectivity": 0.006069833333333333, "wkb_geometries_returned": "72838"}]}