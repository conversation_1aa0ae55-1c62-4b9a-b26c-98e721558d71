{"opensource": {"error": "Test failed: \nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\nnote: run with `RUST_BACKTRACE=1` environment variable to display a backtrace\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144177), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144160), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144166), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144152), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144184), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144200), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144206), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144145), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144194), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144133), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144137), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144217), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144222), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144228), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144235), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144245), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144254), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance/src/io/exec/take.rs:269:40:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144164), \"called `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144166), \\\"called `Option::unwrap()` on a `None` value\\\", ...)\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144261), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144744), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144779), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144817), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144854), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144885), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144916), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance/src/io/exec/take.rs:269:40:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144911), \"called `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(144916), \\\"called `Option::unwrap()` on a `None` value\\\", ...)\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(145838), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance/src/io/exec/take.rs:269:40:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(145828), \"called `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(145838), \\\"called `Option::unwrap()` on a `None` value\\\", ...)\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1882:30:\ncalled `Result::unwrap()` on an `Err` value: JoinError::Panic(Id(146500), \"called `Option::unwrap()` on a `None` value\", ...)\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/decoder.rs:1880:9:\n`async fn` resumed after panicking\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\nTraceback (most recent call last):\n  File \"/home/<USER>/Work/terrafloww/geo-research/test_comprehensive_wkb.py\", line 634, in <module>\n    main()\n  File \"/home/<USER>/Work/terrafloww/geo-research/test_comprehensive_wkb.py\", line 620, in main\n    tester.print_comparison(results)\n  File \"/home/<USER>/Work/terrafloww/geo-research/test_comprehensive_wkb.py\", line 557, in print_comparison\n    print(f\"{'Write Time':<20} {os_create['write_time']:<15.3f} {custom_create['write_time']:<15.3f} {((os_create['write_time'] - custom_create['write_time']) / os_create['write_time'] * 100):+.1f}%\")\n                                ~~~~~~~~~^^^^^^^^^^^^^^\nKeyError: 'write_time'\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n\nthread 'lance_background_thread' panicked at /home/<USER>/work/lance/lance/rust/lance-encoding/src/encodings/physical.rs:169:44:\ncalled `Option::unwrap()` on a `None` value\n"}, "custom": {"error": "Test failed: Traceback (most recent call last):\n  File \"/home/<USER>/Work/terrafloww/geo-research/test_comprehensive_wkb.py\", line 634, in <module>\n    main()\n  File \"/home/<USER>/Work/terrafloww/geo-research/test_comprehensive_wkb.py\", line 620, in main\n    tester.print_comparison(results)\n  File \"/home/<USER>/Work/terrafloww/geo-research/test_comprehensive_wkb.py\", line 557, in print_comparison\n    print(f\"{'Write Time':<20} {os_create['write_time']:<15.3f} {custom_create['write_time']:<15.3f} {((os_create['write_time'] - custom_create['write_time']) / os_create['write_time'] * 100):+.1f}%\")\n                                ~~~~~~~~~^^^^^^^^^^^^^^\nKeyError: 'write_time'\n"}}