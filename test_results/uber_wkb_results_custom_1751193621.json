{"mode": "custom", "data_info": {"rows": 4000000, "columns": 20, "pickup_wkb_count": "3939334", "dropoff_wkb_count": "3940378", "memory_mb": 1286.7178812026978}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 3939334, "validity_rate": 0.9848335, "min_lon": -85.50151062011719, "max_lon": 0.09812700003385544, "min_lat": -0.07020799815654755, "max_lat": 81.53500366210938}, "dropoff_bounds": {"valid_rows": 3940378, "validity_rate": 0.9850945, "min_lon": -83.43216705322266, "max_lon": 0.10140500217676163, "min_lat": -0.0662280023097992, "max_lat": 81.53500366210938}}, "write_performance": {"time_seconds": 0.0, "dataset_size_mb": 414.9764585494995, "compression_ratio": 3.1007009065050726, "write_success": true, "existing_dataset": true}, "wkb_column_performance": {"time_seconds": 0.0937967300415039, "pickup_wkb_count": 4000000, "dropoff_wkb_count": 4000000, "wkb_success": true, "skipped": false}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "description": "High-density Manhattan area for WKB testing", "filter": "pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.74 AND pickup_latitude <= 40.78", "time_seconds": 0.2022385597229004, "rows_returned": 1208458, "selectivity": 0.3021145, "wkb_geometries_returned": "1208458"}, {"name": "Small Area WKB", "description": "Small selective area for WKB spatial optimization", "filter": "pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND pickup_latitude >= 40.755 AND pickup_latitude <= 40.765", "time_seconds": 0.08197355270385742, "rows_returned": 221711, "selectivity": 0.05542775, "wkb_geometries_returned": "221711"}, {"name": "Airport Area WKB", "description": "JFK Airport vicinity for WKB testing", "filter": "dropoff_longitude >= -73.82 AND dropoff_longitude <= -73.76 AND dropoff_latitude >= 40.63 AND dropoff_latitude <= 40.67", "time_seconds": 0.050559282302856445, "rows_returned": 24424, "selectivity": 0.006106, "wkb_geometries_returned": "24424"}]}