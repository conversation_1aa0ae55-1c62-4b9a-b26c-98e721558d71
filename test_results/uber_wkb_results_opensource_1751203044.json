{"mode": "opensource", "data_info": {"rows": 5000000, "columns": 20, "pickup_wkb_count": "4943874", "dropoff_wkb_count": "4945425", "memory_mb": 1647.2954578399658}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 4943874, "validity_rate": 0.9887748, "min_lon": -107.33740234375, "max_lon": 98.68528747558594, "min_lat": -1.0473469495773315, "max_lat": 81.2884521484375}, "dropoff_bounds": {"valid_rows": 4945425, "validity_rate": 0.989085, "min_lon": -94.20185089111328, "max_lon": 98.68528747558594, "min_lat": -7.33510684967041, "max_lat": 81.56687927246094}}, "write_performance": {"time_seconds": 1.699981689453125, "dataset_size_mb": 560.4975566864014, "compression_ratio": 2.9389877586239477, "write_success": true, "existing_dataset": false}, "wkb_column_performance": {"time_seconds": 1.8249754905700684, "pickup_wkb_count": 4943874, "dropoff_wkb_count": 4945425, "wkb_success": true, "skipped": false}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "description": "High-density Manhattan area for WKB testing", "filter": "pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.74 AND pickup_latitude <= 40.78", "time_seconds": 0.6576590538024902, "rows_returned": 1502304, "selectivity": 0.3004608, "wkb_geometries_returned": "1502304"}, {"name": "Small Area WKB", "description": "Small selective area for WKB spatial optimization", "filter": "pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND pickup_latitude >= 40.755 AND pickup_latitude <= 40.765", "time_seconds": 0.20342564582824707, "rows_returned": 273625, "selectivity": 0.054725, "wkb_geometries_returned": "273625"}, {"name": "Airport Area WKB", "description": "JFK Airport vicinity for WKB testing", "filter": "dropoff_longitude >= -73.82 AND dropoff_longitude <= -73.76 AND dropoff_latitude >= 40.63 AND dropoff_latitude <= 40.67", "time_seconds": 0.16052508354187012, "rows_returned": 36336, "selectivity": 0.0072672, "wkb_geometries_returned": "36273"}]}