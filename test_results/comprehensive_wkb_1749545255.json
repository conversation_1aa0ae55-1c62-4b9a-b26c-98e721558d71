{"test_timestamp": 1749545250.760332, "max_rows": 1000000, "modes": {"opensource": {"creation": {"mode": "opensource", "success": true, "rows": 1000000, "wkb_time": 0.47266173362731934, "write_time": 0.6590335369110107, "dataset_size_mb": 150.001482963562, "pickup_wkb_count": "984878", "dropoff_wkb_count": "985136"}, "spatial": {"mode": "opensource", "success": true, "total_rows": 1000000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.15020394325256348, "rows_returned": 302646, "selectivity": 0.302646, "wkb_count": "302646", "success": true}, {"name": "Small Area", "time_seconds": 0.044798851013183594, "rows_returned": 55369, "selectivity": 0.055369, "wkb_count": "55369", "success": true}, {"name": "Airport Area", "time_seconds": 0.02680373191833496, "rows_returned": 6061, "selectivity": 0.006061, "wkb_count": "6061", "success": true}]}, "duckdb": {"mode": "opensource", "success": false, "error": "Invalid Input Error: arrow_scan: get_next failed(): IOError: Io error: External error: Encountered internal error. Please file a bug report at https://github.com/lancedb/lance/issues. Error decoding batch: Invalid user input: Row range exceeds available offsets, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/encodings/geopage.rs:1897:31, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/decoder.rs:2585:31"}}, "custom": {"creation": {"mode": "custom", "success": true, "rows": 1000000, "wkb_time": 0.4538393020629883, "write_time": 0.6358728408813477, "dataset_size_mb": 150.0014820098877, "pickup_wkb_count": "984878", "dropoff_wkb_count": "985136"}, "spatial": {"mode": "custom", "success": true, "total_rows": 1000000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.15529322624206543, "rows_returned": 302646, "selectivity": 0.302646, "wkb_count": "302646", "success": true}, {"name": "Small Area", "time_seconds": 0.043062448501586914, "rows_returned": 55369, "selectivity": 0.055369, "wkb_count": "55369", "success": true}, {"name": "Airport Area", "time_seconds": 0.022306203842163086, "rows_returned": 6061, "selectivity": 0.006061, "wkb_count": "6061", "success": true}]}, "duckdb": {"mode": "custom", "success": false, "error": "Invalid Input Error: arrow_scan: get_next failed(): IOError: Io error: External error: Encountered internal error. Please file a bug report at https://github.com/lancedb/lance/issues. Error decoding batch: Invalid user input: Row range exceeds available offsets, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/encodings/geopage.rs:1897:31, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/decoder.rs:2585:31"}}}}