{"mode": "custom", "data_info": {"rows": 100000, "columns": 20, "pickup_wkb_count": "98573", "dropoff_wkb_count": "98611", "memory_mb": 32.17514228820801}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 98573, "validity_rate": 0.98573, "min_lon": -76.11469268798828, "max_lon": 0.005876999814063311, "min_lat": -0.00596500001847744, "max_lat": 81.53500366210938}, "dropoff_bounds": {"valid_rows": 98611, "validity_rate": 0.98611, "min_lon": -76.09280395507812, "max_lon": 0.004495000001043081, "min_lat": -0.005026999861001968, "max_lat": 81.53500366210938}}, "write_performance": {"time_seconds": 0.37505578994750977, "dataset_size_mb": 10.406545639038086, "compression_ratio": 3.091817727441598, "write_success": true, "existing_dataset": false}, "wkb_column_performance": {"time_seconds": 0.025402069091796875, "pickup_wkb_count": 100000, "dropoff_wkb_count": 100000, "wkb_success": true, "skipped": false}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "description": "High-density Manhattan area for WKB testing", "filter": "pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.74 AND pickup_latitude <= 40.78", "time_seconds": 0.0655207633972168, "rows_returned": 30119, "selectivity": 0.30119, "wkb_geometries_returned": "30119"}, {"name": "Small Area WKB", "description": "Small selective area for WKB spatial optimization", "filter": "pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND pickup_latitude >= 40.755 AND pickup_latitude <= 40.765", "time_seconds": 0.038620710372924805, "rows_returned": 5518, "selectivity": 0.05518, "wkb_geometries_returned": "5518"}, {"name": "Airport Area WKB", "description": "JFK Airport vicinity for WKB testing", "filter": "dropoff_longitude >= -73.82 AND dropoff_longitude <= -73.76 AND dropoff_latitude >= 40.63 AND dropoff_latitude <= 40.67", "time_seconds": 0.02793097496032715, "rows_returned": 632, "selectivity": 0.00632, "wkb_geometries_returned": "632"}]}