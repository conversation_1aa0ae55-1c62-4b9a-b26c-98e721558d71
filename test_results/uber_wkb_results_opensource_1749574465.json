{"mode": "opensource", "data_info": {"rows": 100000, "columns": 20, "pickup_wkb_count": "98573", "dropoff_wkb_count": "98611", "memory_mb": 32.17514228820801}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 98573, "validity_rate": 0.98573, "min_lon": -76.11469268798828, "max_lon": 0.005876999814063311, "min_lat": -0.00596500001847744, "max_lat": 81.53500366210938}, "dropoff_bounds": {"valid_rows": 98611, "validity_rate": 0.98611, "min_lon": -76.09280395507812, "max_lon": 0.004495000001043081, "min_lat": -0.005026999861001968, "max_lat": 81.53500366210938}}, "write_performance": {"time_seconds": 0.4510490894317627, "dataset_size_mb": 11.131645202636719, "compression_ratio": 2.8904211104919857, "write_success": true, "existing_dataset": false}, "wkb_column_performance": {"time_seconds": 0.0, "pickup_wkb_count": 0, "dropoff_wkb_count": 0, "wkb_success": false, "skipped": false, "error": "External error: RuntimeError: Task was aborted"}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "error": "External error: RuntimeError: Task was aborted"}, {"name": "Small Area WKB", "error": "External error: RuntimeError: Task was aborted"}, {"name": "Airport Area WKB", "error": "External error: RuntimeError: Task was aborted"}]}