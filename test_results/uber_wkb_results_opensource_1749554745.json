{"mode": "opensource", "data_info": {"rows": 12000000, "columns": 20, "pickup_wkb_count": 11800000, "dropoff_wkb_count": 11800000, "memory_mb": 3200}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 12000000, "validity_rate": 0.98}, "dropoff_bounds": {"valid_rows": 12000000, "validity_rate": 0.98}}, "write_performance": {"time_seconds": 0.0, "dataset_size_mb": 1343.3380031585693, "compression_ratio": 2.382125714061458, "write_success": true, "existing_dataset": true}, "wkb_column_performance": {"time_seconds": 4.449336528778076, "pickup_wkb_count": 11818356, "dropoff_wkb_count": 11821480, "wkb_success": true, "skipped": false}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "error": "External error: KeyboardInterrupt: "}, {"name": "Small Area WKB", "description": "Small selective area for WKB spatial optimization", "filter": "pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND pickup_latitude >= 40.755 AND pickup_latitude <= 40.765", "time_seconds": 1.8121802806854248, "rows_returned": 665771, "selectivity": 0.055480916666666664, "wkb_geometries_returned": "665771"}, {"name": "Airport Area WKB", "description": "JFK Airport vicinity for WKB testing", "filter": "dropoff_longitude >= -73.82 AND dropoff_longitude <= -73.76 AND dropoff_latitude >= 40.63 AND dropoff_latitude <= 40.67", "time_seconds": 1.406252145767212, "rows_returned": 72838, "selectivity": 0.006069833333333333, "wkb_geometries_returned": "72758"}]}