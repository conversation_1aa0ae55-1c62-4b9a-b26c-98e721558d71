{"mode": "custom", "data_info": {"rows": 100000, "columns": 20, "pickup_wkb_count": "98573", "dropoff_wkb_count": "98611", "memory_mb": 32.17514228820801}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 98573, "validity_rate": 0.98573, "min_lon": -76.11469268798828, "max_lon": 0.005876999814063311, "min_lat": -0.00596500001847744, "max_lat": 81.53500366210938}, "dropoff_bounds": {"valid_rows": 98611, "validity_rate": 0.98611, "min_lon": -76.09280395507812, "max_lon": 0.004495000001043081, "min_lat": -0.005026999861001968, "max_lat": 81.53500366210938}}, "write_performance": {"time_seconds": 0.07206320762634277, "dataset_size_mb": 15.030852317810059, "compression_ratio": 2.140606640788006, "write_success": true, "existing_dataset": false}, "wkb_column_performance": {"error": "Io error: External error: Encountered internal error. Please file a bug report at https://github.com/lancedb/lance/issues. Error decoding batch: Invalid user input: Row range exceeds available offsets, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/encodings/geopage.rs:1897:31, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/decoder.rs:2585:31", "wkb_success": false}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "description": "High-density Manhattan area for WKB testing", "filter": "pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.74 AND pickup_latitude <= 40.78", "time_seconds": 0.01747894287109375, "rows_returned": 30119, "selectivity": 0.30119, "wkb_geometries_returned": "30119"}, {"name": "Small Area WKB", "description": "Small selective area for WKB spatial optimization", "filter": "pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND pickup_latitude >= 40.755 AND pickup_latitude <= 40.765", "time_seconds": 0.011085987091064453, "rows_returned": 5518, "selectivity": 0.05518, "wkb_geometries_returned": "5518"}, {"name": "Airport Area WKB", "description": "JFK Airport vicinity for WKB testing", "filter": "dropoff_longitude >= -73.82 AND dropoff_longitude <= -73.76 AND dropoff_latitude >= 40.63 AND dropoff_latitude <= 40.67", "time_seconds": 0.011492729187011719, "rows_returned": 632, "selectivity": 0.00632, "wkb_geometries_returned": "632"}]}