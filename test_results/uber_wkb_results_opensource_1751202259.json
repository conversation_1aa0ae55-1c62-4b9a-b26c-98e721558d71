{"mode": "opensource", "data_info": {"rows": 3000000, "columns": 20, "pickup_wkb_count": "2966425", "dropoff_wkb_count": "2967380", "memory_mb": 988.3762760162354}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 2966425, "validity_rate": 0.9888083333333333, "min_lon": -99.81666564941406, "max_lon": 2.9505269527435303, "min_lat": -0.8511329889297485, "max_lat": 81.2884521484375}, "dropoff_bounds": {"valid_rows": 2967380, "validity_rate": 0.9891266666666667, "min_lon": -88.75077056884766, "max_lon": 38.79637908935547, "min_lat": -7.33510684967041, "max_lat": 81.56687927246094}}, "write_performance": {"time_seconds": 0.0, "dataset_size_mb": 414.9764595031738, "compression_ratio": 2.3817646841933113, "write_success": true, "existing_dataset": true}, "wkb_column_performance": {"time_seconds": 0.0, "pickup_wkb_count": 0, "dropoff_wkb_count": 0, "wkb_success": false, "skipped": false, "error": "External error: RuntimeError: Task was aborted"}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "error": "External error: RuntimeError: Task was aborted"}, {"name": "Small Area WKB", "error": "External error: RuntimeError: Task was aborted"}, {"name": "Airport Area WKB", "error": "External error: RuntimeError: Task was aborted"}]}