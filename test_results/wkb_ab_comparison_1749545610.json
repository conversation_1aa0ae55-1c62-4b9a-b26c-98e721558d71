{"opensource": {"error": "Test failed: Traceback (most recent call last):\n  File \"/home/<USER>/Work/terrafloww/geo-research/test_comprehensive_wkb.py\", line 15, in <module>\n    import duckdb\nModuleNotFoundError: No module named 'duckdb'\n"}, "custom": {"error": "Test failed: Traceback (most recent call last):\n  File \"/home/<USER>/Work/terrafloww/geo-research/test_comprehensive_wkb.py\", line 552, in <module>\n    main()\n  File \"/home/<USER>/Work/terrafloww/geo-research/test_comprehensive_wkb.py\", line 535, in main\n    results = tester.run_comprehensive_test(args.max_rows)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Work/terrafloww/geo-research/test_comprehensive_wkb.py\", line 454, in run_comprehensive_test\n    spatial_result = self.test_spatial_performance_subprocess(config)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ComprehensiveWKBTester' object has no attribute 'test_spatial_performance_subprocess'\n"}}