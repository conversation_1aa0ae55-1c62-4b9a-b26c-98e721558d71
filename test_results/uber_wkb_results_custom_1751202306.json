{"mode": "custom", "data_info": {"rows": 3000000, "columns": 20, "pickup_wkb_count": "2966425", "dropoff_wkb_count": "2967380", "memory_mb": 988.3762760162354}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 2966425, "validity_rate": 0.9888083333333333, "min_lon": -99.81666564941406, "max_lon": 2.9505269527435303, "min_lat": -0.8511329889297485, "max_lat": 81.2884521484375}, "dropoff_bounds": {"valid_rows": 2967380, "validity_rate": 0.9891266666666667, "min_lon": -88.75077056884766, "max_lon": 38.79637908935547, "min_lat": -7.33510684967041, "max_lat": 81.56687927246094}}, "write_performance": {"time_seconds": 0.0, "dataset_size_mb": 414.9764585494995, "compression_ratio": 2.381764689666942, "write_success": true, "existing_dataset": true}, "wkb_column_performance": {"time_seconds": 0.0798039436340332, "pickup_wkb_count": 4000000, "dropoff_wkb_count": 4000000, "wkb_success": true, "skipped": false}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "description": "High-density Manhattan area for WKB testing", "filter": "pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.74 AND pickup_latitude <= 40.78", "time_seconds": 0.17635130882263184, "rows_returned": 1208458, "selectivity": 0.4028193333333333, "wkb_geometries_returned": "1208458"}, {"name": "Small Area WKB", "description": "Small selective area for WKB spatial optimization", "filter": "pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND pickup_latitude >= 40.755 AND pickup_latitude <= 40.765", "time_seconds": 0.08507299423217773, "rows_returned": 221711, "selectivity": 0.07390366666666667, "wkb_geometries_returned": "221711"}, {"name": "Airport Area WKB", "description": "JFK Airport vicinity for WKB testing", "filter": "dropoff_longitude >= -73.82 AND dropoff_longitude <= -73.76 AND dropoff_latitude >= 40.63 AND dropoff_latitude <= 40.67", "time_seconds": 0.06063652038574219, "rows_returned": 24424, "selectivity": 0.008141333333333334, "wkb_geometries_returned": "24424"}]}