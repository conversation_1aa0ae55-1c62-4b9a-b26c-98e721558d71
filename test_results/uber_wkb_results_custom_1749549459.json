{"mode": "custom", "data_info": {"rows": 12000000, "columns": 20, "pickup_wkb_count": 11800000, "dropoff_wkb_count": 11800000, "memory_mb": 3200}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 12000000, "validity_rate": 0.98}, "dropoff_bounds": {"valid_rows": 12000000, "validity_rate": 0.98}}, "write_performance": {"time_seconds": 0.0, "dataset_size_mb": 1800.3355340957642, "compression_ratio": 1.7774464478407526, "write_success": true, "existing_dataset": true}, "wkb_column_performance": {"time_seconds": 0.0, "pickup_wkb_count": 0, "dropoff_wkb_count": 0, "wkb_success": false, "skipped": true, "reason": "Known decoder bug: Row range exceeds available offsets"}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "error": "External error: RuntimeError: Task was aborted"}, {"name": "Small Area WKB", "error": "External error: RuntimeError: Task was aborted"}, {"name": "Airport Area WKB", "error": "External error: RuntimeError: Task was aborted"}]}