{"test_timestamp": 1749545234.07276, "max_rows": 100000, "modes": {"opensource": {"creation": {"mode": "opensource", "success": true, "rows": 100000, "wkb_time": 0.05012774467468262, "write_time": 0.07103323936462402, "dataset_size_mb": 15.030853271484375, "pickup_wkb_count": "98573", "dropoff_wkb_count": "98611"}, "spatial": {"mode": "opensource", "success": true, "total_rows": 100000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.08056902885437012, "rows_returned": 30119, "selectivity": 0.30119, "wkb_count": "30119", "success": true}, {"name": "Small Area", "time_seconds": 0.015386104583740234, "rows_returned": 5518, "selectivity": 0.05518, "wkb_count": "5518", "success": true}, {"name": "Airport Area", "time_seconds": 0.009140968322753906, "rows_returned": 632, "selectivity": 0.00632, "wkb_count": "632", "success": true}]}, "duckdb": {"mode": "opensource", "success": false, "error": "Invalid Input Error: arrow_scan: get_next failed(): IOError: Io error: External error: Encountered internal error. Please file a bug report at https://github.com/lancedb/lance/issues. Error decoding batch: Invalid user input: Row range exceeds available offsets, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/encodings/geopage.rs:1897:31, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/decoder.rs:2585:31"}}, "custom": {"creation": {"mode": "custom", "success": true, "rows": 100000, "wkb_time": 0.045866966247558594, "write_time": 0.08656167984008789, "dataset_size_mb": 15.030852317810059, "pickup_wkb_count": "98573", "dropoff_wkb_count": "98611"}, "spatial": {"mode": "custom", "success": true, "total_rows": 100000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.03740286827087402, "rows_returned": 30119, "selectivity": 0.30119, "wkb_count": "30119", "success": true}, {"name": "Small Area", "time_seconds": 0.016753196716308594, "rows_returned": 5518, "selectivity": 0.05518, "wkb_count": "5518", "success": true}, {"name": "Airport Area", "time_seconds": 0.012616634368896484, "rows_returned": 632, "selectivity": 0.00632, "wkb_count": "632", "success": true}]}, "duckdb": {"mode": "custom", "success": false, "error": "Invalid Input Error: arrow_scan: get_next failed(): IOError: Io error: External error: Encountered internal error. Please file a bug report at https://github.com/lancedb/lance/issues. Error decoding batch: Invalid user input: Row range exceeds available offsets, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/encodings/geopage.rs:1897:31, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/decoder.rs:2585:31"}}}}