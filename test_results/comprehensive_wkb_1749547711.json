{"test_timestamp": 1749547702.874538, "max_rows": 12000000, "modes": {"opensource": {"creation": {"mode": "opensource", "success": true, "rows": 12000000, "dataset_size_mb": 1343.3380031585693, "existing_dataset": true}, "spatial": {"mode": "opensource", "success": true, "total_rows": 12000000, "queries": [{"name": "Manhattan Core", "time_seconds": 2.166677713394165, "rows_returned": 3628392, "selectivity": 0.302366, "wkb_count": "3628392", "success": true}, {"name": "Small Area", "time_seconds": 0.7498910427093506, "rows_returned": 665771, "selectivity": 0.055480916666666664, "wkb_count": "665771", "success": true}, {"name": "Airport Area", "time_seconds": 1.0075099468231201, "rows_returned": 72838, "selectivity": 0.006069833333333333, "wkb_count": "72758", "success": true}]}, "duckdb": {"mode": "opensource", "success": true, "wkb_validation": {"time_seconds": 0.16184735298156738, "total_rows": 11818356, "wkb_count": 11818356, "avg_wkb_length": 21.0}, "geometry_conversion": {"time_seconds": 0.15957283973693848, "valid_geometries": 11818356}, "spatial_intersection": {"time_seconds": 0.7840762138366699, "matching_geometries": 6529263}}}, "custom": {"creation": {"mode": "custom", "success": true, "rows": 12000000, "dataset_size_mb": 1800.3355340957642, "existing_dataset": true}, "spatial": {"mode": "custom", "success": true, "total_rows": 12000000, "queries": [{"name": "Manhattan Core", "time_seconds": 1.1172418594360352, "rows_returned": 3628392, "selectivity": 0.302366, "wkb_count": "3628392", "success": true}, {"name": "Small Area", "time_seconds": 0.47121143341064453, "rows_returned": 665771, "selectivity": 0.055480916666666664, "wkb_count": "665771", "success": true}, {"name": "Airport Area", "time_seconds": 0.23042607307434082, "rows_returned": 72838, "selectivity": 0.006069833333333333, "wkb_count": "72838", "success": true}]}, "duckdb": {"mode": "custom", "success": false, "error": "Invalid Input Error: arrow_scan: get_next failed(): IOError: Io error: External error: Encountered internal error. Please file a bug report at https://github.com/lancedb/lance/issues. Error decoding batch: Invalid user input: Row range exceeds available offsets, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/encodings/geopage.rs:1897:31, /home/<USER>/Work/terrafloww/geo-research/lance-internal/rust/lance-encoding/src/decoder.rs:2585:31"}}}}