{"mode": "custom", "data_info": {"rows": 12000000, "columns": 20, "pickup_wkb_count": 11800000, "dropoff_wkb_count": 11800000, "memory_mb": 3200}, "spatial_analysis": {"pickup_bounds": {"valid_rows": 12000000, "validity_rate": 0.98}, "dropoff_bounds": {"valid_rows": 12000000, "validity_rate": 0.98}}, "write_performance": {"time_seconds": 0.0, "dataset_size_mb": 1245.1747817993164, "compression_ratio": 2.569920341123437, "write_success": true, "existing_dataset": true}, "wkb_column_performance": {"time_seconds": 0.3702549934387207, "pickup_wkb_count": 12000000, "dropoff_wkb_count": 12000000, "wkb_success": true, "skipped": false}, "spatial_query_performance": [{"name": "Manhattan Core WKB", "description": "High-density Manhattan area for WKB testing", "filter": "pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.74 AND pickup_latitude <= 40.78", "time_seconds": 2.791419267654419, "rows_returned": 3628392, "selectivity": 0.302366, "wkb_geometries_returned": "3628392"}, {"name": "Small Area WKB", "description": "Small selective area for WKB spatial optimization", "filter": "pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND pickup_latitude >= 40.755 AND pickup_latitude <= 40.765", "time_seconds": 1.5462887287139893, "rows_returned": 665771, "selectivity": 0.055480916666666664, "wkb_geometries_returned": "665771"}, {"name": "Airport Area WKB", "description": "JFK Airport vicinity for WKB testing", "filter": "dropoff_longitude >= -73.82 AND dropoff_longitude <= -73.76 AND dropoff_latitude >= 40.63 AND dropoff_latitude <= 40.67", "time_seconds": 1.2467706203460693, "rows_returned": 72838, "selectivity": 0.006069833333333333, "wkb_geometries_returned": "72838"}]}