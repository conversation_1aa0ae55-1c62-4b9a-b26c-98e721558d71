{"test_timestamp": 1749543502.3904057, "max_rows": 1000000, "modes": {"opensource": {"creation": {"mode": "opensource", "success": true, "rows": 1000000, "wkb_time": 0.4711780548095703, "write_time": 0.6101882457733154, "dataset_size_mb": 150.001482963562, "pickup_wkb_count": "984878", "dropoff_wkb_count": "985136"}, "spatial": {"mode": "opensource", "success": true, "total_rows": 1000000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.11156702041625977, "rows_returned": 302646, "selectivity": 0.302646, "wkb_count": "302646", "success": true}, {"name": "Small Area", "time_seconds": 0.03144431114196777, "rows_returned": 55369, "selectivity": 0.055369, "wkb_count": "55369", "success": true}, {"name": "Airport Area", "time_seconds": 0.03920125961303711, "rows_returned": 6061, "selectivity": 0.006061, "wkb_count": "6061", "success": true}]}, "duckdb": {"mode": "opensource", "success": true, "sample_size": 10000, "wkb_validation": {"time_seconds": 0.0023474693298339844, "total_rows": 10000, "wkb_count": 10000, "avg_wkb_length": 21.0}, "geometry_conversion": {"time_seconds": 0.0024204254150390625, "valid_geometries": 10000}, "spatial_intersection": {"time_seconds": 0.003143787384033203, "matching_geometries": 5530}}}, "custom": {"creation": {"mode": "custom", "success": true, "rows": 1000000, "wkb_time": 0.4626731872558594, "write_time": 0.6071124076843262, "dataset_size_mb": 150.001482963562, "pickup_wkb_count": "984878", "dropoff_wkb_count": "985136"}, "spatial": {"mode": "custom", "success": true, "total_rows": 1000000, "queries": [{"name": "Manhattan Core", "time_seconds": 0.10689806938171387, "rows_returned": 302646, "selectivity": 0.302646, "wkb_count": "302646", "success": true}, {"name": "Small Area", "time_seconds": 0.04186701774597168, "rows_returned": 55369, "selectivity": 0.055369, "wkb_count": "55369", "success": true}, {"name": "Airport Area", "time_seconds": 0.020282268524169922, "rows_returned": 6061, "selectivity": 0.006061, "wkb_count": "6061", "success": true}]}, "duckdb": {"mode": "custom", "success": true, "sample_size": 10000, "wkb_validation": {"time_seconds": 0.0025436878204345703, "total_rows": 10000, "wkb_count": 10000, "avg_wkb_length": 21.0}, "geometry_conversion": {"time_seconds": 0.0024957656860351562, "valid_geometries": 10000}, "spatial_intersection": {"time_seconds": 0.0031347274780273438, "matching_geometries": 5530}}}}}