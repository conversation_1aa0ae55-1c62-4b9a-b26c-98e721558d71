# Lance Patterns Reference - What We Learned

## 🏗️ **Lance Encoding Architecture**

### **Physical Encoding Pattern**
```rust
// 1. PageScheduler trait - handles I/O scheduling
impl PageScheduler for MyPageScheduler {
    fn schedule_ranges(
        &self,
        ranges: &[Range<u64>],
        scheduler: &Arc<dyn EncodingsIo>,
        top_level_row: u64,
    ) -> BoxFuture<'static, Result<Box<dyn PrimitivePageDecoder>>> {
        // Async I/O scheduling logic
    }
}

// 2. PrimitivePageDecoder - handles actual decoding
impl PrimitivePageDecoder for MyPageDecoder {
    fn decode(&self, rows_to_skip: u64, num_rows: u64) -> Result<DataBlock> {
        // Decode logic returning DataBlock
    }
}
```

### **Decompressor Traits Pattern**
```rust
// Multiple decompressor traits for different use cases
impl BlockDecompressor for MyDecompressor {
    fn decompress(&self, data: <PERSON><PERSON><PERSON><PERSON>, num_values: u64) -> Result<DataBlock> {
        // Block-level decompression
    }
}

impl MiniBlockDecompressor for MyDecompressor {
    fn decompress(&self, data: <PERSON><PERSON>uffer, num_values: u64) -> Result<DataBlock> {
        // Mini-block decompression (usually delegates to BlockDecompressor)
    }
}

impl FixedPerValueDecompressor for MyDecompressor {
    fn decompress(&self, data: FixedWidthDataBlock, num_values: u64) -> Result<DataBlock> {
        // Fixed-width per-value decompression
    }
    
    fn bits_per_value(&self) -> u64 {
        // Return bits per value
    }
}
```

### **ArrayEncoder Pattern**
```rust
impl ArrayEncoder for MyEncoder {
    fn encode(
        &self,
        data: DataBlock,
        data_type: &DataType,
        buffer_index: &mut u32,
    ) -> Result<EncodedArray> {
        // Encoding logic
        Ok(EncodedArray { data, encoding })
    }
}
```

## 🔧 **Decoder Dispatch Pattern**

### **CoreDecompressorStrategy**
```rust
impl DecompressorStrategy for CoreDecompressorStrategy {
    fn create_block_decompressor(&self, description: &pb::ArrayEncoding) -> Result<Box<dyn BlockDecompressor>> {
        match description.array_encoding.as_ref().unwrap() {
            pb::array_encoding::ArrayEncoding::GeoPage(ref g) => {
                Ok(Box::new(GeoPageDecompressor::new(g.clone())))
            }
            // ... other encodings
        }
    }
    
    // Similar for create_miniblock_decompressor, create_fixed_per_value_decompressor
}
```

### **Physical Encoding Dispatch**
```rust
// In encodings/physical.rs
pub fn get_buffer_decoder(encoding: &pb::array_encoding::ArrayEncoding, buffers: &PageBuffers) -> Box<dyn PageScheduler> {
    match encoding {
        pb::array_encoding::ArrayEncoding::GeoPage(geo_page) => {
            Box::new(GeoPageReader::try_new(geo_page.clone()).unwrap())
        }
        // ... other encodings
    }
}
```

## 📦 **Protobuf Integration Pattern**

### **Module Structure**
```rust
// In format.rs
pub mod pb {
    // Include proto module first so it's available for encodings
    pub mod proto {
        include!(concat!(env!("OUT_DIR"), "/lance.proto.rs"));
    }
    
    // Now include encodings which references proto
    include!(concat!(env!("OUT_DIR"), "/lance.encodings.rs"));
}
```

### **Build.rs Pattern**
```rust
// In build.rs
prost_build.compile_protos(
    &["protos/encodings.proto", "protos/geopage.proto"],
    &["protos"],
)?;
```

## 🎯 **Field Metadata Triggering**

### **Encoder Selection**
```rust
// In CoreArrayEncodingStrategy::choose_array_encoder
fn choose_array_encoder(
    arrays: &[ArrayRef],
    data_type: &DataType,
    data_size: u64,
    use_dict_encoding: bool,
    version: LanceFileVersion,
    field_meta: Option<&HashMap<String, String>>,
) -> Result<Box<dyn ArrayEncoder>> {
    // Check field metadata for custom encoding
    if let Some(meta) = field_meta {
        if meta.get("encoding").map(|s| s.as_str()) == Some("geopage") {
            let field_name = meta.get("name").map_or("geopage_field", |v| v);
            let field = lance_core::datatypes::Field::new_arrow(
                field_name,
                data_type.clone(),
                true,
            )?;
            return Ok(Box::new(GeoPageWriter::try_new(&field)?));
        }
    }
    // ... rest of encoder selection logic
}
```

## 🗃️ **DataBlock Patterns**

### **Common DataBlock Types**
```rust
// Fixed-width data (primitives)
DataBlock::FixedWidth(FixedWidthDataBlock {
    data: LanceBuffer,
    bits_per_value: u64,
    num_values: u64,
    block_info: BlockInfo::new(),
})

// Variable-width data (strings, binary)
DataBlock::VariableWidth(VariableWidthBlock {
    bits_per_offset: 32,  // or 64
    offsets: LanceBuffer,  // offset array
    data: LanceBuffer,     // actual data
    num_values: u64,
    block_info: BlockInfo::new(),
})

// Nullable data
DataBlock::Nullable(NullableDataBlock {
    data: Box<DataBlock>,
    nulls: LanceBuffer,    // validity bitmap
    block_info: BlockInfo::new(),
})
```

### **LanceBuffer Usage**
```rust
// Create from Vec<u8>
let buffer = LanceBuffer::from(vec![1u8, 2, 3, 4]);

// Create from bytes with specific alignment
let buffer = LanceBuffer::from_bytes(bytes, alignment);

// For offsets, convert u32 to bytes
let offset_bytes: Vec<u8> = vec![0u32, data.len() as u32]
    .into_iter()
    .flat_map(|x| x.to_le_bytes())
    .collect();
let offsets = LanceBuffer::from(offset_bytes);
```

## 🔄 **Async Patterns**

### **BoxFuture Pattern**
```rust
use futures::future::BoxFuture;
use futures::FutureExt;

fn schedule_ranges(&self, ...) -> BoxFuture<'static, Result<Box<dyn PrimitivePageDecoder>>> {
    async move {
        // Async logic here
        Ok(Box::new(MyDecoder { ... }) as Box<dyn PrimitivePageDecoder>)
    }.boxed()
}
```

## 🛠️ **Common Pitfalls We Encountered**

### **1. Trait Signature Mismatches**
```rust
// WRONG: Missing num_values parameter
fn decompress(&self, data: LanceBuffer) -> Result<DataBlock>

// CORRECT: Include all required parameters
fn decompress(&self, data: LanceBuffer, num_values: u64) -> Result<DataBlock>
```

### **2. Import Path Issues**
```rust
// WRONG: LanceBuffer is not in lance_core
use lance_core::utils::lance_buffer::LanceBuffer;

// CORRECT: LanceBuffer is in lance-encoding crate
use crate::buffer::LanceBuffer;
```

### **3. Field API Differences**
```rust
// WRONG: Field::new doesn't exist
let field = lance_core::datatypes::Field::new(name, data_type, nullable);

// CORRECT: Use new_arrow for Arrow integration
let field = lance_core::datatypes::Field::new_arrow(name, data_type, nullable)?;
```

### **4. Borrow Checker Issues**
```rust
// WRONG: freeze() moves self.buffer
let page = self.buffer.freeze();
let root_page = self.write_root_page(); // Error: self partially moved

// CORRECT: Clone or restructure to avoid partial moves
let buffer_len = self.buffer.len();
let page = self.buffer.freeze();
// Use buffer_len instead of accessing self.buffer later
```

## 📚 **Key Files to Study**

1. **`encodings/physical/basic.rs`** - Simple PageScheduler pattern
2. **`encodings/physical/value.rs`** - ValueDecompressor implementation  
3. **`decoder.rs`** - CoreDecompressorStrategy dispatch
4. **`encoder.rs`** - CoreArrayEncodingStrategy patterns
5. **`format.rs`** - Protobuf integration and utilities

This reference should help us implement GeoPage correctly following Lance's established patterns!
