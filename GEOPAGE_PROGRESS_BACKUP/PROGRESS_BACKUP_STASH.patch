diff --git a/rust/lance-encoding/src/decoder.rs b/rust/lance-encoding/src/decoder.rs
index d13bfe32..64d66ddf 100644
--- a/rust/lance-encoding/src/decoder.rs
+++ b/rust/lance-encoding/src/decoder.rs
@@ -255,7 +255,7 @@ use crate::encodings::physical::bitpack_fastlanes::InlineBitpacking;
 use crate::encodings::physical::block_compress::CompressedBufferEncoder;
 use crate::encodings::physical::fsst::{FsstMiniBlockDecompressor, FsstPerValueDecompressor};
 use crate::encodings::physical::struct_encoding::PackedStructFixedWidthMiniBlockDecompressor;
-use crate::encodings::geopage::GeoPageReader;
+// use crate::encodings::geopage::GeoPageReader;
 use crate::encodings::physical::value::{ConstantDecompressor, ValueDecompressor};
 use crate::encodings::physical::{Column<PERSON>uff<PERSON>, FileBuffers};
 use crate::format::pb::{self, column_encoding};
diff --git a/rust/lance-encoding/src/encoder.rs b/rust/lance-encoding/src/encoder.rs
index c10dd09a..01bb99e3 100644
--- a/rust/lance-encoding/src/encoder.rs
+++ b/rust/lance-encoding/src/encoder.rs
@@ -531,11 +531,12 @@ impl CoreArrayEncodingStrategy {
         if let Some(meta) = field_meta {
             if meta.get("encoding").map(|s| s.as_str()) == Some("geopage") {
                 // Create a field from the metadata for GeoPageWriter
-                let field = lance_core::datatypes::Field::new(
-                    meta.get("name").unwrap_or("geopage_field"),
-                    data_type.try_into()?,
+                let field_name = meta.get("name").map_or("geopage_field", |v| v);
+                let field = lance_core::datatypes::Field::new_arrow(
+                    field_name,
+                    data_type.clone(),
                     true, // nullable
-                );
+                )?;
                 return Ok(Box::new(crate::encodings::geopage::GeoPageWriter::try_new(&field)?));
             }
         }
diff --git a/rust/lance-encoding/src/encodings/geopage.rs b/rust/lance-encoding/src/encodings/geopage.rs
index d5039693..19815db4 100644
--- a/rust/lance-encoding/src/encodings/geopage.rs
+++ b/rust/lance-encoding/src/encodings/geopage.rs
@@ -18,10 +18,14 @@ use lance_core::utils::bit::pad_bytes;
 use lance_core::Result;
 
 use crate::data::DataBlock;
-use crate::decoder::{BlockDecompressor, MiniBlockDecompressor};
+use crate::decoder::{BlockDecompressor, FixedPerValueDecompressor, MiniBlockDecompressor, PageScheduler, PrimitivePageDecoder};
 use crate::encoder::{ArrayEncoder, EncodedArray};
 use crate::format::pb;
-use lance_core::utils::lance_buffer::LanceBuffer;
+use crate::buffer::LanceBuffer;
+use crate::EncodingsIo;
+use snafu::location;
+use std::ops::Range;
+use futures::future::BoxFuture;
 
 const GEO_PAGE_SIZE: usize = 4096;
 
@@ -85,20 +89,20 @@ pub struct GeoPageWriter {
     schema: Arc<Schema>,
     buffer: BytesMut,
     pages: Vec<Bytes>,
-    field: Field,
+    field: lance_core::datatypes::Field,
     quadtree_entries: Vec<QuadTreeEntry>,
     current_offset: u64,
 }
 
 impl GeoPageWriter {
     /// Create a new writer for the provided field.
-    pub fn try_new(field: &Field) -> Result<Self> {
+    pub fn try_new(field: &lance_core::datatypes::Field) -> Result<Self> {
         // For now, we'll create a simple schema from the field
         // In the future, this might be more sophisticated
         let arrow_field = arrow_schema::Field::new(
-            field.name(),
-            field.data_type().try_into()?,
-            field.nullable(),
+            &field.name,
+            field.data_type().clone().into(),
+            field.nullable,
         );
         let schema = Arc::new(Schema::new(vec![arrow_field]));
 
@@ -219,14 +223,12 @@ impl GeoPageWriter {
     pub fn finish(mut self) -> Vec<Bytes> {
         // Flush any remaining data
         if !self.buffer.is_empty() {
-            let page_length = {
-                let pad = pad_bytes::<GEO_PAGE_SIZE>(self.buffer.len());
-                self.buffer.extend(std::iter::repeat(0u8).take(pad));
-                let page = self.buffer.freeze();
-                let length = page.len() as u64;
-                self.pages.push(page);
-                length
-            };
+            let buffer_len = self.buffer.len();
+            let pad = pad_bytes::<GEO_PAGE_SIZE>(buffer_len);
+            self.buffer.extend(std::iter::repeat(0u8).take(pad));
+            let page = self.buffer.freeze();
+            let page_length = page.len() as u64;
+            self.pages.push(page);
 
             // Add final page to quadtree if we have entries
             if let Some(last_entry) = self.quadtree_entries.last() {
@@ -240,9 +242,12 @@ impl GeoPageWriter {
             }
         }
 
+        // Clone the quadtree entries before calling write_root_page
+        let quadtree_entries = self.quadtree_entries.clone();
+
         // Write the quadtree root page (page-0)
-        if !self.quadtree_entries.is_empty() {
-            let root_page = self.write_root_page();
+        if !quadtree_entries.is_empty() {
+            let root_page = self.write_root_page_from_entries(&quadtree_entries);
             // Insert root page at the beginning
             let mut all_pages = vec![root_page];
             all_pages.extend(self.pages);
@@ -253,9 +258,9 @@ impl GeoPageWriter {
     }
 
     /// Write the quadtree root page (page-0)
-    fn write_root_page(&self) -> Bytes {
+    fn write_root_page_from_entries(&self, entries: &[QuadTreeEntry]) -> Bytes {
         // Sort entries by quadkey for efficient spatial queries
-        let mut sorted_entries = self.quadtree_entries.clone();
+        let mut sorted_entries = entries.to_vec();
         sorted_entries.sort_by_key(|entry| entry.quadkey);
 
         // Pack entries into 16-byte records
@@ -290,14 +295,15 @@ impl ArrayEncoder for GeoPageWriter {
         // and let the actual encoding happen during the write process.
 
         // Create the GeoPage encoding descriptor
-        let geo_page_array = pb::lance::proto::GeoPageArray {
-            header: Some(pb::lance::proto::GeoPageHeader {
+        let geo_page_array = pb::proto::GeoPageArray {
+            header: Some(pb::proto::GeoPageHeader {
                 quadkey: 0, // Will be set during actual encoding
                 xmin: 0.0,
                 ymin: 0.0,
                 xmax: 0.0,
                 ymax: 0.0,
                 bvh: vec![].into(),
+                root_offset: 0, // Page-0 is at offset 0
             }),
             payload: vec![].into(), // Will be filled during actual encoding
         };
@@ -313,13 +319,13 @@ impl ArrayEncoder for GeoPageWriter {
 /// Reader for GeoPage encoded data.
 #[derive(Debug)]
 pub struct GeoPageReader {
-    geo_page_array: pb::lance::proto::GeoPageArray,
+    geo_page_array: pb::proto::GeoPageArray,
     quadtree_nodes: Option<Vec<QuadTreeNode>>,
 }
 
 impl GeoPageReader {
     /// Create a new GeoPage reader from the protobuf description.
-    pub fn try_new(geo_page_array: pb::lance::proto::GeoPageArray) -> Result<Self> {
+    pub fn try_new(geo_page_array: pb::proto::GeoPageArray) -> Result<Self> {
         Ok(Self {
             geo_page_array,
             quadtree_nodes: None,
@@ -489,20 +495,38 @@ impl GeoPageReader {
     }
 }
 
+impl PageScheduler for GeoPageReader {
+    fn schedule_ranges(
+        &self,
+        _ranges: &[Range<u64>],
+        _scheduler: &std::sync::Arc<dyn EncodingsIo>,
+        _top_level_row: u64,
+    ) -> BoxFuture<'static, Result<Box<dyn PrimitivePageDecoder>>> {
+        // For now, GeoPage doesn't support the traditional page scheduling approach
+        // This is a placeholder implementation
+        Box::pin(async move {
+            Err(lance_core::Error::Internal {
+                message: "GeoPage does not support traditional page scheduling yet".to_string(),
+                location: location!(),
+            })
+        })
+    }
+}
+
 /// A decompressor for GeoPage encoded data
 #[derive(Debug)]
 pub struct GeoPageDecompressor {
-    geo_page_array: pb::lance::proto::GeoPageArray,
+    geo_page_array: pb::proto::GeoPageArray,
 }
 
 impl GeoPageDecompressor {
-    pub fn new(geo_page_array: pb::lance::proto::GeoPageArray) -> Self {
+    pub fn new(geo_page_array: pb::proto::GeoPageArray) -> Self {
         Self { geo_page_array }
     }
 }
 
 impl BlockDecompressor for GeoPageDecompressor {
-    fn decompress(&self, data: LanceBuffer) -> Result<DataBlock> {
+    fn decompress(&self, data: LanceBuffer, num_values: u64) -> Result<DataBlock> {
         // For GeoPage, the data contains the Arrow IPC payload
         // We need to deserialize it and return as a DataBlock
 
@@ -515,10 +539,20 @@ impl BlockDecompressor for GeoPageDecompressor {
         // 5. Convert to appropriate DataBlock
 
         // For now, return the raw data as a variable-width block
+        // We need to create proper offsets for variable width data
+        // Convert u32 offsets to bytes for LanceBuffer
+        let offset_bytes: Vec<u8> = vec![0u32, data.len() as u32]
+            .into_iter()
+            .flat_map(|x| x.to_le_bytes())
+            .collect();
+        let offsets = LanceBuffer::from(offset_bytes);
+
         Ok(DataBlock::VariableWidth(
             crate::data::VariableWidthBlock {
+                bits_per_offset: 32,
+                offsets,
                 data,
-                num_values: 1, // Each page is treated as one value for now
+                num_values,
                 block_info: crate::data::BlockInfo::new(),
             }
         ))
@@ -526,9 +560,22 @@ impl BlockDecompressor for GeoPageDecompressor {
 }
 
 impl MiniBlockDecompressor for GeoPageDecompressor {
-    fn decompress(&self, data: LanceBuffer, _num_values: u64) -> Result<DataBlock> {
+    fn decompress(&self, data: LanceBuffer, num_values: u64) -> Result<DataBlock> {
         // Delegate to the block decompressor
-        BlockDecompressor::decompress(self, data)
+        BlockDecompressor::decompress(self, data, num_values)
+    }
+}
+
+impl FixedPerValueDecompressor for GeoPageDecompressor {
+    fn decompress(&self, data: crate::data::FixedWidthDataBlock, num_values: u64) -> Result<DataBlock> {
+        // Convert FixedWidthDataBlock to LanceBuffer and delegate to block decompressor
+        BlockDecompressor::decompress(self, data.data, num_values)
+    }
+
+    fn bits_per_value(&self) -> u64 {
+        // GeoPage uses variable-width data, so we return a default value
+        // This is not really used for GeoPage since it's variable-width
+        8 // 1 byte per value as a default
     }
 }
 
diff --git a/rust/lance-encoding/src/encodings/physical.rs b/rust/lance-encoding/src/encodings/physical.rs
index 0d82bbd8..f2999d2c 100644
--- a/rust/lance-encoding/src/encodings/physical.rs
+++ b/rust/lance-encoding/src/encodings/physical.rs
@@ -295,8 +295,8 @@ pub fn decoder_from_array_encoding(
         pb::array_encoding::ArrayEncoding::BitpackedForNonNeg(bitpacked) => {
             get_bitpacked_for_non_neg_buffer_decoder(bitpacked, buffers)
         }
-        pb::array_encoding::ArrayEncoding::GeoPage(_) => {
-            Box::new(GeoPageReader)
+        pb::array_encoding::ArrayEncoding::GeoPage(geo_page) => {
+            Box::new(GeoPageReader::try_new(geo_page.clone()).unwrap())
         }
         // Currently there is no way to encode struct nullability and structs are encoded with a "header" column
         // (that has no data).  We never actually decode that column and so this branch is never actually encountered.
diff --git a/rust/lance-encoding/src/format.rs b/rust/lance-encoding/src/format.rs
index faa72d9e..49354ff9 100644
--- a/rust/lance-encoding/src/format.rs
+++ b/rust/lance-encoding/src/format.rs
@@ -11,6 +11,21 @@ pub mod pb {
     #![allow(improper_ctypes)]
     #![allow(clippy::upper_case_acronyms)]
     #![allow(clippy::use_self)]
+
+    // Include the proto module first so it's available for encodings
+    pub mod proto {
+        #![allow(clippy::all)]
+        #![allow(non_upper_case_globals)]
+        #![allow(non_camel_case_types)]
+        #![allow(non_snake_case)]
+        #![allow(unused)]
+        #![allow(improper_ctypes)]
+        #![allow(clippy::upper_case_acronyms)]
+        #![allow(clippy::use_self)]
+        include!(concat!(env!("OUT_DIR"), "/lance.proto.rs"));
+    }
+
+    // Now include encodings which references proto
     include!(concat!(env!("OUT_DIR"), "/lance.encodings.rs"));
 }
 
@@ -20,10 +35,11 @@ use pb::{
     full_zip_layout,
     nullable::{AllNull, NoNull, Nullability, SomeNull},
     page_layout::Layout,
+    proto::{GeoPageArray, GeoPageHeader},
     AllNullLayout, ArrayEncoding, Binary, Bitpacked, BitpackedForNonNeg, Block, Dictionary,
-    FixedSizeBinary, FixedSizeList, Flat, Fsst, InlineBitpacking, MiniBlockLayout, Nullable,
-    OutOfLineBitpacking, PackedStruct, PackedStructFixedWidthMiniBlock, PageLayout, RepDefLayer,
-    Variable,
+    FixedSizeBinary, FixedSizeList, Flat, Fsst, InlineBitpacking,
+    MiniBlockLayout, Nullable, OutOfLineBitpacking, PackedStruct, PackedStructFixedWidthMiniBlock,
+    PageLayout, RepDefLayer, Variable,
 };
 
 use crate::{
@@ -266,17 +282,19 @@ impl ProtobufUtils {
         ymax: f64,
         bvh: Vec<u8>,
         payload: Vec<u8>,
+        root_offset: u32,
     ) -> ArrayEncoding {
         ArrayEncoding {
             array_encoding: Some(ArrayEncodingEnum::GeoPage(
-                pb::lance::proto::GeoPageArray {
-                    header: Some(pb::lance::proto::GeoPageHeader {
+                GeoPageArray {
+                    header: Some(GeoPageHeader {
                         quadkey,
                         xmin,
                         ymin,
                         xmax,
                         ymax,
                         bvh: bvh.into(),
+                        root_offset,
                     }),
                     payload: payload.into(),
                 },
