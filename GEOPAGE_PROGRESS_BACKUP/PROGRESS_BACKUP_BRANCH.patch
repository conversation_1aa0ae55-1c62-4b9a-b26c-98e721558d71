diff --git a/protos/encodings.proto b/protos/encodings.proto
index 185a0963..1c8293ce 100644
--- a/protos/encodings.proto
+++ b/protos/encodings.proto
@@ -6,6 +6,7 @@ syntax = "proto3";
 package lance.encodings;
  
 import "google/protobuf/empty.proto";
+import "geopage.proto";
 
 // This file contains a specification for encodings that can be used
 // to store and load Arrow data into a Lance file.
@@ -295,6 +296,7 @@ message ArrayEncoding {
         Variable variable = 16;
         PackedStructFixedWidthMiniBlock packed_struct_fixed_width_mini_block = 17;
         Block block = 18;
+        lance.proto.GeoPageArray geo_page = 19;
     }
 }
 
diff --git a/protos/geopage.proto b/protos/geopage.proto
new file mode 100644
index 00000000..b5ec85c1
--- /dev/null
+++ b/protos/geopage.proto
@@ -0,0 +1,21 @@
+// SPDX-License-Identifier: Apache-2.0
+// SPDX-FileCopyrightText: Copyright The Lance Authors
+
+syntax = "proto3";
+
+package lance.proto;
+
+// Header information for a page of geospatial data.
+message GeoPageHeader {
+  uint64 quadkey = 1;
+  double xmin    = 2;
+  double ymin    = 3;
+  double xmax    = 4;
+  double ymax    = 5;
+  bytes  bvh     = 6;   // optional, for future GPU
+}
+
+message GeoPageArray {
+  GeoPageHeader header = 1;
+  bytes         payload = 2;   // Arrow IPC, 4 KiB padded
+}
diff --git a/rust/lance-encoding/build.rs b/rust/lance-encoding/build.rs
index 37efdcbc..f54acb5e 100644
--- a/rust/lance-encoding/build.rs
+++ b/rust/lance-encoding/build.rs
@@ -14,7 +14,10 @@ fn main() -> Result<()> {
     prost_build.protoc_arg("--experimental_allow_proto3_optional");
     prost_build.enable_type_names();
     prost_build.bytes(["."]); // Enable Bytes type for all messages to avoid Vec clones.
-    prost_build.compile_protos(&["./protos/encodings.proto"], &["./protos"])?;
+    prost_build.compile_protos(
+        &["protos/encodings.proto", "protos/geopage.proto"],
+        &["protos"],
+    )?;
 
     Ok(())
 }
diff --git a/rust/lance-encoding/src/decoder.rs b/rust/lance-encoding/src/decoder.rs
index 97cf3c13..dc65f4b5 100644
--- a/rust/lance-encoding/src/decoder.rs
+++ b/rust/lance-encoding/src/decoder.rs
@@ -255,6 +255,7 @@ use crate::encodings::physical::bitpack_fastlanes::InlineBitpacking;
 use crate::encodings::physical::block_compress::CompressedBufferEncoder;
 use crate::encodings::physical::fsst::{FsstMiniBlockDecompressor, FsstPerValueDecompressor};
 use crate::encodings::physical::struct_encoding::PackedStructFixedWidthMiniBlockDecompressor;
+use crate::encodings::geopage::GeoPageReader;
 use crate::encodings::physical::value::{ConstantDecompressor, ValueDecompressor};
 use crate::encodings::physical::{ColumnBuffers, FileBuffers};
 use crate::format::pb::{self, column_encoding};
diff --git a/rust/lance-encoding/src/encodings.rs b/rust/lance-encoding/src/encodings.rs
index 22fc5ac8..ad9cd42b 100644
--- a/rust/lance-encoding/src/encodings.rs
+++ b/rust/lance-encoding/src/encodings.rs
@@ -3,3 +3,4 @@
 
 pub mod logical;
 pub mod physical;
+pub mod geopage;
diff --git a/rust/lance-encoding/src/encodings/geopage.rs b/rust/lance-encoding/src/encodings/geopage.rs
new file mode 100644
index 00000000..f5f54620
--- /dev/null
+++ b/rust/lance-encoding/src/encodings/geopage.rs
@@ -0,0 +1,81 @@
+// SPDX-License-Identifier: Apache-2.0
+// SPDX-FileCopyrightText: Terrafloww Labs, 2025
+
+//! Utilities for encoding geospatial pages.
+//!
+//! NOTE: This is an experimental implementation. The protobuf definitions
+//! for `GeoPageHeader` and `GeoPageArray` are expected to be generated from
+//! `geopage.proto` during the build process.
+
+use std::io::Cursor;
+use std::sync::Arc;
+
+use arrow::ipc::writer::StreamWriter;
+use arrow_array::RecordBatch;
+use arrow_schema::Schema;
+use bytes::{Bytes, BytesMut};
+use lance_core::utils::bit::pad_bytes;
+use lance_core::Result;
+
+const GEO_PAGE_SIZE: usize = 4096;
+
+/// Writes Arrow IPC data into fixed size 4KiB chunks.
+#[derive(Debug)]
+pub struct GeoPageWriter {
+    schema: Arc<Schema>,
+    buffer: BytesMut,
+    pages: Vec<Bytes>,
+}
+
+impl GeoPageWriter {
+    /// Create a new writer for the provided schema.
+    pub fn new(schema: Arc<Schema>) -> Self {
+        Self {
+            schema,
+            buffer: BytesMut::new(),
+            pages: Vec::new(),
+        }
+    }
+
+    /// Write a record batch of data.
+    pub fn write_batch(&mut self, batch: &RecordBatch) -> Result<()> {
+        let mut cursor = Cursor::new(Vec::new());
+        {
+            let mut writer = StreamWriter::try_new(&mut cursor, &self.schema)?;
+            writer.write(batch)?;
+            writer.finish()?;
+        }
+        self.buffer.extend_from_slice(&cursor.into_inner());
+        self.flush_partial();
+        Ok(())
+    }
+
+    /// Flush any full 4KiB chunks to the page list.
+    fn flush_partial(&mut self) {
+        while self.buffer.len() >= GEO_PAGE_SIZE {
+            let chunk = self.buffer.split_to(GEO_PAGE_SIZE);
+            self.pages.push(chunk.freeze());
+        }
+    }
+
+    /// Finish writing and return all pages.
+    pub fn finish(mut self) -> Vec<Bytes> {
+        if !self.buffer.is_empty() {
+            let pad = pad_bytes::<GEO_PAGE_SIZE>(self.buffer.len());
+            self.buffer.extend(std::iter::repeat(0u8).take(pad));
+            self.pages.push(self.buffer.freeze());
+        }
+        self.pages
+    }
+}
+
+/// Reader for GeoPage encoded data.
+#[derive(Debug)]
+pub struct GeoPageReader;
+
+impl GeoPageReader {
+    /// Load an Arrow array from raw page bytes.
+    pub fn load_array(&self, _data: &[u8], _schema: Arc<Schema>) -> Result<RecordBatch> {
+        unimplemented!("GeoPage array loading not implemented yet")
+    }
+}
diff --git a/rust/lance-encoding/src/encodings/physical.rs b/rust/lance-encoding/src/encodings/physical.rs
index 284315b9..0d82bbd8 100644
--- a/rust/lance-encoding/src/encodings/physical.rs
+++ b/rust/lance-encoding/src/encodings/physical.rs
@@ -14,6 +14,7 @@ use self::{
 };
 use crate::buffer::LanceBuffer;
 use crate::encodings::physical::block_compress::CompressionScheme;
+use crate::encodings::geopage::GeoPageReader;
 use crate::{
     decoder::PageScheduler,
     format::pb::{self, PackedStruct},
@@ -294,6 +295,9 @@ pub fn decoder_from_array_encoding(
         pb::array_encoding::ArrayEncoding::BitpackedForNonNeg(bitpacked) => {
             get_bitpacked_for_non_neg_buffer_decoder(bitpacked, buffers)
         }
+        pb::array_encoding::ArrayEncoding::GeoPage(_) => {
+            Box::new(GeoPageReader)
+        }
         // Currently there is no way to encode struct nullability and structs are encoded with a "header" column
         // (that has no data).  We never actually decode that column and so this branch is never actually encountered.
         //
diff --git a/rust/lance-file/build.rs b/rust/lance-file/build.rs
index 05b791fa..4ebffde3 100644
--- a/rust/lance-file/build.rs
+++ b/rust/lance-file/build.rs
@@ -15,11 +15,12 @@ fn main() -> Result<()> {
     prost_build.extern_path(".lance.encodings", "::lance_encoding::format::pb");
     prost_build.compile_protos(
         &[
-            "./protos/file.proto",
-            "./protos/file2.proto",
-            "./protos/encodings.proto",
+            "protos/file.proto",
+            "protos/file2.proto",
+            "protos/encodings.proto",
+            "protos/geopage.proto",
         ],
-        &["./protos"],
+        &["protos"],
     )?;
 
     Ok(())
