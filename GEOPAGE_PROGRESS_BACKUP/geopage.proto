// SPDX-License-Identifier: Apache-2.0
// SPDX-FileCopyrightText: Copyright The Lance Authors

syntax = "proto3";

package lance.proto;

// Header information for a page of geospatial data.
message GeoPageHeader {
  uint64 quadkey = 1;
  double xmin    = 2;
  double ymin    = 3;
  double xmax    = 4;
  double ymax    = 5;
  bytes  bvh     = 6;   // optional, for future GPU
}

message GeoPageArray {
  GeoPageHeader header = 1;
  bytes         payload = 2;   // Arrow IPC, 4 KiB padded
}
