// SPDX-License-Identifier: Apache-2.0
// SPDX-FileCopyrightText: Terrafloww Labs, 2025

//! Utilities for encoding geospatial pages.
//!
//! NOTE: This is an experimental implementation. The protobuf definitions
//! for `GeoPageHeader` and `GeoPageArray` are expected to be generated from
//! `geopage.proto` during the build process.

use std::io::Cursor;
use std::sync::Arc;

use arrow::ipc::writer::StreamWriter;
use arrow_array::RecordBatch;
use arrow_schema::Schema;
use bytes::{Bytes, BytesMut};
use lance_core::utils::bit::pad_bytes;
use lance_core::Result;

const GEO_PAGE_SIZE: usize = 4096;

/// Writes Arrow IPC data into fixed size 4KiB chunks.
#[derive(Debug)]
pub struct GeoPageWriter {
    schema: Arc<Schema>,
    buffer: BytesMut,
    pages: Vec<Bytes>,
}

impl GeoPageWriter {
    /// Create a new writer for the provided schema.
    pub fn new(schema: Arc<Schema>) -> Self {
        Self {
            schema,
            buffer: BytesMut::new(),
            pages: Vec::new(),
        }
    }

    /// Write a record batch of data.
    pub fn write_batch(&mut self, batch: &RecordBatch) -> Result<()> {
        let mut cursor = Cursor::new(Vec::new());
        {
            let mut writer = StreamWriter::try_new(&mut cursor, &self.schema)?;
            writer.write(batch)?;
            writer.finish()?;
        }
        self.buffer.extend_from_slice(&cursor.into_inner());
        self.flush_partial();
        Ok(())
    }

    /// Flush any full 4KiB chunks to the page list.
    fn flush_partial(&mut self) {
        while self.buffer.len() >= GEO_PAGE_SIZE {
            let chunk = self.buffer.split_to(GEO_PAGE_SIZE);
            self.pages.push(chunk.freeze());
        }
    }

    /// Finish writing and return all pages.
    pub fn finish(mut self) -> Vec<Bytes> {
        if !self.buffer.is_empty() {
            let pad = pad_bytes::<GEO_PAGE_SIZE>(self.buffer.len());
            self.buffer.extend(std::iter::repeat(0u8).take(pad));
            self.pages.push(self.buffer.freeze());
        }
        self.pages
    }
}

/// Reader for GeoPage encoded data.
#[derive(Debug)]
pub struct GeoPageReader;

impl GeoPageReader {
    /// Load an Arrow array from raw page bytes.
    pub fn load_array(&self, _data: &[u8], _schema: Arc<Schema>) -> Result<RecordBatch> {
        unimplemented!("GeoPage array loading not implemented yet")
    }
}
