#!/usr/bin/env python3
"""
Comprehensive analysis of Uber/NYC Taxi data for GeoPage performance testing.
This script analyzes the spatial properties, data distribution, and characteristics
of the real-world taxi trip data to optimize our GeoPage encoding strategy.
"""

import os
import pandas as pd
import numpy as np
import pyarrow.parquet as pq
from pathlib import Path
import json
import time
from typing import Dict, List, Tuple, Any
import matplotlib.pyplot as plt
import seaborn as sns

class UberDataAnalyzer:
    def __init__(self, data_root: str = "uber_data"):
        self.data_root = Path(data_root)
        self.analysis_results = {}
        
    def discover_data_files(self) -> List[Path]:
        """Discover all parquet files in the data directory."""
        parquet_files = []
        for root, dirs, files in os.walk(self.data_root):
            for file in files:
                if file.endswith('.parquet'):
                    parquet_files.append(Path(root) / file)
        return sorted(parquet_files)
    
    def analyze_single_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyze a single parquet file for spatial and data properties."""
        print(f"📊 Analyzing: {file_path}")
        
        try:
            # Read parquet file
            df = pd.read_parquet(file_path)
            
            # Basic statistics
            analysis = {
                'file_path': str(file_path),
                'file_size_mb': file_path.stat().st_size / (1024 * 1024),
                'num_rows': len(df),
                'num_columns': len(df.columns),
                'columns': list(df.columns),
                'dtypes': {col: str(dtype) for col, dtype in df.dtypes.items()},
            }
            
            # Identify spatial columns
            spatial_columns = self.identify_spatial_columns(df)
            analysis['spatial_columns'] = spatial_columns
            
            # Analyze spatial properties if spatial columns exist
            if spatial_columns:
                analysis['spatial_analysis'] = self.analyze_spatial_properties(df, spatial_columns)
            
            # Memory usage
            analysis['memory_usage_mb'] = df.memory_usage(deep=True).sum() / (1024 * 1024)
            
            # Sample data
            analysis['sample_rows'] = df.head(3).to_dict('records')
            
            return analysis
            
        except Exception as e:
            return {
                'file_path': str(file_path),
                'error': str(e),
                'file_size_mb': file_path.stat().st_size / (1024 * 1024) if file_path.exists() else 0
            }
    
    def identify_spatial_columns(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """Identify spatial columns (latitude, longitude) in the dataframe."""
        spatial_cols = {
            'longitude': [],
            'latitude': [],
            'coordinate_pairs': []
        }
        
        # Common patterns for spatial columns
        lon_patterns = ['lon', 'lng', 'longitude', 'pickup_longitude', 'dropoff_longitude']
        lat_patterns = ['lat', 'latitude', 'pickup_latitude', 'dropoff_latitude']
        
        for col in df.columns:
            col_lower = col.lower()
            if any(pattern in col_lower for pattern in lon_patterns):
                spatial_cols['longitude'].append(col)
            elif any(pattern in col_lower for pattern in lat_patterns):
                spatial_cols['latitude'].append(col)
        
        # Identify coordinate pairs
        lon_cols = spatial_cols['longitude']
        lat_cols = spatial_cols['latitude']
        
        for lon_col in lon_cols:
            for lat_col in lat_cols:
                if ('pickup' in lon_col.lower() and 'pickup' in lat_col.lower()) or \
                   ('dropoff' in lon_col.lower() and 'dropoff' in lat_col.lower()) or \
                   (lon_col.replace('lon', '').replace('lng', '') == lat_col.replace('lat', '')):
                    spatial_cols['coordinate_pairs'].append((lon_col, lat_col))
        
        return spatial_cols
    
    def analyze_spatial_properties(self, df: pd.DataFrame, spatial_cols: Dict[str, List[str]]) -> Dict[str, Any]:
        """Analyze spatial properties of the data."""
        spatial_analysis = {}
        
        for lon_col, lat_col in spatial_cols['coordinate_pairs']:
            pair_name = f"{lon_col}_{lat_col}"
            
            # Filter out invalid coordinates
            valid_mask = (
                (df[lon_col].notna()) & 
                (df[lat_col].notna()) &
                (df[lon_col] != 0) & 
                (df[lat_col] != 0) &
                (df[lon_col].between(-180, 180)) &
                (df[lat_col].between(-90, 90))
            )
            
            valid_data = df[valid_mask]
            
            if len(valid_data) == 0:
                continue
                
            lon_data = valid_data[lon_col]
            lat_data = valid_data[lat_col]
            
            spatial_analysis[pair_name] = {
                'valid_rows': len(valid_data),
                'invalid_rows': len(df) - len(valid_data),
                'validity_rate': len(valid_data) / len(df),
                
                # Bounding box
                'bbox': {
                    'min_lon': float(lon_data.min()),
                    'max_lon': float(lon_data.max()),
                    'min_lat': float(lat_data.min()),
                    'max_lat': float(lat_data.max()),
                },
                
                # Statistical properties
                'longitude_stats': {
                    'mean': float(lon_data.mean()),
                    'std': float(lon_data.std()),
                    'median': float(lon_data.median()),
                    'q25': float(lon_data.quantile(0.25)),
                    'q75': float(lon_data.quantile(0.75)),
                },
                'latitude_stats': {
                    'mean': float(lat_data.mean()),
                    'std': float(lat_data.std()),
                    'median': float(lat_data.median()),
                    'q25': float(lat_data.quantile(0.25)),
                    'q75': float(lat_data.quantile(0.75)),
                },
                
                # Spatial clustering analysis
                'spatial_clustering': self.analyze_spatial_clustering(lon_data, lat_data),
            }
        
        return spatial_analysis
    
    def analyze_spatial_clustering(self, lon_data: pd.Series, lat_data: pd.Series) -> Dict[str, Any]:
        """Analyze spatial clustering patterns for quadtree optimization."""
        # Grid-based density analysis
        lon_bins = np.linspace(lon_data.min(), lon_data.max(), 20)
        lat_bins = np.linspace(lat_data.min(), lat_data.max(), 20)
        
        hist, _, _ = np.histogram2d(lon_data, lat_data, bins=[lon_bins, lat_bins])
        
        return {
            'grid_density_stats': {
                'mean_density': float(hist.mean()),
                'max_density': float(hist.max()),
                'std_density': float(hist.std()),
                'empty_cells': int(np.sum(hist == 0)),
                'total_cells': int(hist.size),
                'sparsity': float(np.sum(hist == 0) / hist.size),
            },
            'coordinate_range': {
                'lon_range': float(lon_data.max() - lon_data.min()),
                'lat_range': float(lat_data.max() - lat_data.min()),
            }
        }
    
    def analyze_all_files(self, max_files: int = None) -> Dict[str, Any]:
        """Analyze all discovered parquet files."""
        files = self.discover_data_files()
        
        if max_files:
            files = files[:max_files]
        
        print(f"🔍 Discovered {len(files)} parquet files")
        
        all_analyses = []
        total_size = 0
        total_rows = 0
        
        for i, file_path in enumerate(files):
            print(f"📊 Progress: {i+1}/{len(files)}")
            analysis = self.analyze_single_file(file_path)
            all_analyses.append(analysis)
            
            if 'num_rows' in analysis:
                total_rows += analysis['num_rows']
                total_size += analysis['file_size_mb']
        
        # Aggregate analysis
        summary = {
            'total_files': len(files),
            'total_size_mb': total_size,
            'total_rows': total_rows,
            'avg_file_size_mb': total_size / len(files) if files else 0,
            'avg_rows_per_file': total_rows / len(files) if files else 0,
            'file_analyses': all_analyses,
        }
        
        # Find common schema
        if all_analyses:
            summary['common_schema'] = self.analyze_common_schema(all_analyses)
        
        return summary
    
    def analyze_common_schema(self, analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze common schema across all files."""
        all_columns = set()
        all_spatial_columns = set()
        
        for analysis in analyses:
            if 'columns' in analysis:
                all_columns.update(analysis['columns'])
            if 'spatial_columns' in analysis:
                for coord_pair in analysis['spatial_columns'].get('coordinate_pairs', []):
                    all_spatial_columns.update(coord_pair)
        
        return {
            'all_columns': sorted(list(all_columns)),
            'spatial_columns': sorted(list(all_spatial_columns)),
            'total_unique_columns': len(all_columns),
        }
    
    def save_analysis(self, analysis: Dict[str, Any], output_file: str = "uber_data_analysis.json"):
        """Save analysis results to JSON file."""
        with open(output_file, 'w') as f:
            json.dump(analysis, f, indent=2, default=str)
        print(f"💾 Analysis saved to: {output_file}")

def main():
    """Main analysis function."""
    print("=" * 80)
    print("🚗 UBER/NYC TAXI DATA ANALYSIS FOR GEOPAGE OPTIMIZATION")
    print("=" * 80)
    
    analyzer = UberDataAnalyzer()
    
    # Analyze all files (limit to first 5 for initial analysis)
    print("🔍 Starting comprehensive data analysis...")
    start_time = time.time()
    
    analysis = analyzer.analyze_all_files(max_files=5)  # Start with 5 files
    
    end_time = time.time()
    analysis['analysis_duration_seconds'] = end_time - start_time
    
    # Save results
    analyzer.save_analysis(analysis)
    
    # Print summary
    print("\n" + "=" * 80)
    print("📊 ANALYSIS SUMMARY")
    print("=" * 80)
    print(f"📁 Total files analyzed: {analysis['total_files']}")
    print(f"💾 Total data size: {analysis['total_size_mb']:.2f} MB")
    print(f"📊 Total rows: {analysis['total_rows']:,}")
    print(f"⏱️  Analysis time: {analysis['analysis_duration_seconds']:.2f}s")
    
    if 'common_schema' in analysis:
        print(f"🗂️  Unique columns: {analysis['common_schema']['total_unique_columns']}")
        print(f"🗺️  Spatial columns: {analysis['common_schema']['spatial_columns']}")
    
    print("\n🎯 Ready for GeoPage performance testing!")
    return analysis

if __name__ == "__main__":
    analysis = main()
