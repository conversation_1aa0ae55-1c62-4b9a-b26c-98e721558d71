#!/usr/bin/env python3
"""
Compare JSON A/B test results between open-source and custom Lance
"""

import json
import sys
from pathlib import Path

def load_json_results(filepath):
    """Load JSON results file"""
    try:
        with open(filepath, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ Error: File not found: {filepath}")
        return None
    except json.JSONDecodeError:
        print(f"❌ Error: Invalid JSON in file: {filepath}")
        return None

def compare_json_results(opensource_file, custom_file):
    """Compare two JSON result files"""
    print("📊 Lance A/B Test Results Comparison")
    print("=" * 50)
    
    # Load both files
    opensource = load_json_results(opensource_file)
    custom = load_json_results(custom_file)
    
    if not opensource or not custom:
        return None
    
    print(f"✅ Loaded open-source results: {opensource['data_info']['rows']:,} rows")
    print(f"✅ Loaded custom results: {custom['data_info']['rows']:,} rows")
    
    # Basic performance comparison
    print(f"\n📊 PERFORMANCE COMPARISON")
    print("-" * 50)
    
    # Write performance
    os_write = opensource['write_performance']['time_seconds']
    custom_write = custom['write_performance']['time_seconds']

    if os_write > 0 and custom_write > 0:
        write_improvement = ((os_write - custom_write) / os_write) * 100
        print(f"📝 Write Performance:")
        print(f"   Open Source: {os_write:.3f}s")
        print(f"   Custom:      {custom_write:.3f}s")
        print(f"   Change:      {write_improvement:+.1f}%")
    else:
        write_improvement = 0
        print(f"📝 Write Performance: N/A (using existing datasets)")
    
    # Dataset size
    os_size = opensource['write_performance']['dataset_size_mb']
    custom_size = custom['write_performance']['dataset_size_mb']
    size_change = ((custom_size - os_size) / os_size) * 100
    
    print(f"\n💾 Dataset Size:")
    print(f"   Open Source: {os_size:.2f}MB")
    print(f"   Custom:      {custom_size:.2f}MB")
    print(f"   Change:      {size_change:+.1f}%")
    
    # # Full scan performance
    # os_scan = opensource['full_scan_performance']['time_seconds']
    # custom_scan = custom['full_scan_performance']['time_seconds']
    # scan_improvement = ((os_scan - custom_scan) / os_scan) * 100
    
    # print(f"\n📖 Full Scan Performance:")
    # print(f"   Open Source: {os_scan:.3f}s")
    # print(f"   Custom:      {custom_scan:.3f}s")
    # print(f"   Improvement: {scan_improvement:+.1f}%")
    
    # Spatial query comparison
    print(f"\n🗺️ SPATIAL QUERY COMPARISON")
    print("-" * 50)
    
    os_spatial = {q['name']: q for q in opensource['spatial_query_performance']}
    custom_spatial = {q['name']: q for q in custom['spatial_query_performance']}
    
    total_improvements = []
    
    for query_name in os_spatial.keys():
        if query_name in custom_spatial:
            os_time = os_spatial[query_name]['time_seconds']
            custom_time = custom_spatial[query_name]['time_seconds']
            os_rows = os_spatial[query_name]['rows_returned']
            custom_rows = custom_spatial[query_name]['rows_returned']
            
            improvement = ((os_time - custom_time) / os_time) * 100
            total_improvements.append(improvement)
            
            print(f"🎯 {query_name}:")
            print(f"   Open Source: {os_time:.3f}s ({os_rows:,} rows)")
            print(f"   Custom:      {custom_time:.3f}s ({custom_rows:,} rows)")
            print(f"   Improvement: {improvement:+.1f}%")
            print()
    
    # Summary
    if total_improvements:
        avg_improvement = sum(total_improvements) / len(total_improvements)
        print(f"📈 SUMMARY")
        print("-" * 20)
        print(f"Average spatial query improvement: {avg_improvement:+.1f}%")
        print(f"Best improvement: {max(total_improvements):+.1f}%")
        print(f"Worst improvement: {min(total_improvements):+.1f}%")
    
    # Additional analysis for custom results
    if 'bbox_query_performance' in custom and custom['bbox_query_performance']:
        print(f"\n🎯 BBOX QUERY PERFORMANCE (Custom Only)")
        print("-" * 40)
        for bbox_query in custom['bbox_query_performance']:
            print(f"   {bbox_query['name']}: {bbox_query['time_seconds']:.3f}s ({bbox_query['rows_returned']:,} rows)")
    
    return {
        'write_improvement': write_improvement,
        # 'scan_improvement': scan_improvement,
        'spatial_improvements': total_improvements,
        'avg_spatial_improvement': avg_improvement if total_improvements else 0
    }

def main():
    if len(sys.argv) != 3:
        print("Usage: python compare_json_results.py <opensource_json> <custom_json>")
        sys.exit(1)
    
    opensource_file = sys.argv[1]
    custom_file = sys.argv[2]
    
    results = compare_json_results(opensource_file, custom_file)
    
    if results:
        print(f"\n🎉 CONCLUSION")
        print("=" * 20)
        if results['avg_spatial_improvement'] > 0:
            print(f"✅ GeoPage optimizations are working!")
            print(f"   Average spatial query speedup: {results['avg_spatial_improvement']:.1f}%")
        else:
            print(f"⚠️  GeoPage optimizations need investigation")
            print(f"   Average spatial query change: {results['avg_spatial_improvement']:.1f}%")

if __name__ == "__main__":
    main()
