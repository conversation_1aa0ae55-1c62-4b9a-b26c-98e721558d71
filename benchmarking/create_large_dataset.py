#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Large Dataset Creation Utility for Lance Benchmarking
Creates multi-million row datasets from NYC taxi data for comprehensive testing.
"""

import pandas as pd
import numpy as np
import argparse
import time
import json
from pathlib import Path
from typing import List, Dict, Any

class LargeDatasetCreator:
    def __init__(self, output_dir: str = "benchmarking/datasets"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Available source files (from analysis)
        self.source_files = [
            "uber_data/nyc-taxi/2009/01/data.parquet",  # 14M rows, 2.9GB
            "uber_data/nyc-taxi/2009/06/data.parquet",  # 14M rows, 3.1GB  
            "uber_data/nyc-taxi/2009/09/data.parquet",  # 14M rows, 3.0GB
        ]
        
    def validate_source_files(self) -> Dict[str, Any]:
        """Validate that source files exist and get their info."""
        file_info = {}
        total_available_rows = 0
        
        print("🔍 Validating source files...")
        
        for file_path in self.source_files:
            if not Path(file_path).exists():
                print(f"❌ Missing source file: {file_path}")
                continue
                
            try:
                # Get actual row count and sample (load full file since parquet is efficient)
                df_full = pd.read_parquet(file_path)
                row_count = len(df_full)

                # Get sample for column info
                df_sample = df_full.head(1000) if len(df_full) > 1000 else df_full.copy()

                del df_full  # Free memory immediately
                
                file_info[file_path] = {
                    'rows': row_count,
                    'columns': len(df_sample.columns),
                    'column_names': list(df_sample.columns),
                    'memory_estimate_mb': row_count * df_sample.memory_usage(deep=True).sum() / (1024 * 1024 * 1000),
                }
                
                total_available_rows += row_count
                print(f"✅ {file_path}: {row_count:,} rows, {len(df_sample.columns)} columns")
                
            except Exception as e:
                print(f"❌ Error reading {file_path}: {e}")
                
        print(f"\n📊 Total available rows: {total_available_rows:,}")
        return file_info
    
    def create_stratified_sample(self, target_rows: int, file_info: Dict[str, Any]) -> pd.DataFrame:
        """Create a stratified sample across multiple source files."""
        print(f"\n🎯 Creating {target_rows:,} row dataset...")
        
        available_files = list(file_info.keys())
        if not available_files:
            raise ValueError("No valid source files available")
        
        # Calculate rows per file (stratified sampling)
        rows_per_file = target_rows // len(available_files)
        remaining_rows = target_rows % len(available_files)
        
        print(f"📋 Sampling strategy:")
        print(f"   Files to use: {len(available_files)}")
        print(f"   Base rows per file: {rows_per_file:,}")
        print(f"   Extra rows for first file: {remaining_rows:,}")
        
        combined_data = []
        total_loaded_rows = 0
        
        for i, file_path in enumerate(available_files):
            # First file gets extra rows if needed
            rows_to_take = rows_per_file + (remaining_rows if i == 0 else 0)
            
            if rows_to_take > file_info[file_path]['rows']:
                rows_to_take = file_info[file_path]['rows']
                print(f"⚠️  Reducing sample from {file_path} to {rows_to_take:,} (file limit)")
            
            print(f"📂 Loading {rows_to_take:,} rows from {Path(file_path).name}...")
            
            # Load and sample data
            df = pd.read_parquet(file_path)
            
            if len(df) > rows_to_take:
                # Random sampling to ensure good distribution
                df_sample = df.sample(n=rows_to_take, random_state=42).reset_index(drop=True)
            else:
                df_sample = df.copy()
            
            combined_data.append(df_sample)
            total_loaded_rows += len(df_sample)
            
            print(f"   ✅ Loaded {len(df_sample):,} rows")
            
            # Free memory
            del df, df_sample
        
        print(f"\n🔄 Combining {len(combined_data)} datasets...")
        final_df = pd.concat(combined_data, ignore_index=True)
        
        # Shuffle to mix temporal patterns from different months
        print("🔀 Shuffling data to mix temporal patterns...")
        final_df = final_df.sample(frac=1, random_state=42).reset_index(drop=True)
        
        print(f"✅ Created combined dataset: {len(final_df):,} rows, {len(final_df.columns)} columns")
        
        return final_df
    
    def analyze_dataset_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze the quality and characteristics of the created dataset."""
        print("\n🔍 Analyzing dataset quality...")
        
        analysis = {
            'basic_stats': {
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'memory_usage_mb': df.memory_usage(deep=True).sum() / (1024 * 1024),
            },
            'spatial_quality': {},
            'data_quality': {}
        }
        
        # Spatial data analysis
        spatial_cols = ['pickup_longitude', 'pickup_latitude', 'dropoff_longitude', 'dropoff_latitude']
        available_spatial = [col for col in spatial_cols if col in df.columns]
        
        if available_spatial:
            print("🗺️  Analyzing spatial data quality...")
            
            for col in available_spatial:
                if col in df.columns:
                    valid_mask = (
                        pd.notna(df[col]) & 
                        (df[col] != 0) & 
                        (df[col] >= -180 if 'longitude' in col else df[col] >= -90) &
                        (df[col] <= 180 if 'longitude' in col else df[col] <= 90)
                    )
                    
                    analysis['spatial_quality'][col] = {
                        'valid_count': int(valid_mask.sum()),
                        'valid_percentage': float(valid_mask.sum() / len(df) * 100),
                        'min_value': float(df[col].min()) if not df[col].empty else None,
                        'max_value': float(df[col].max()) if not df[col].empty else None,
                    }
        
        # Data completeness analysis
        print("📊 Analyzing data completeness...")
        for col in df.columns:
            non_null_count = df[col].notna().sum()
            analysis['data_quality'][col] = {
                'non_null_count': int(non_null_count),
                'completeness_percentage': float(non_null_count / len(df) * 100),
            }
        
        return analysis
    
    def save_dataset(self, df: pd.DataFrame, target_rows: int, analysis: Dict[str, Any]) -> str:
        """Save the dataset and metadata."""
        # Generate filename
        size_label = f"{target_rows//1000000}M" if target_rows >= 1000000 else f"{target_rows//1000}K"
        timestamp = int(time.time())
        filename = f"uber_combined_{size_label}_rows_{timestamp}.parquet"
        filepath = self.output_dir / filename
        
        print(f"\n💾 Saving dataset to {filepath}...")
        
        # Save dataset
        start_time = time.time()
        df.to_parquet(filepath, compression='snappy')
        save_time = time.time() - start_time
        
        # Get file size
        file_size_mb = filepath.stat().st_size / (1024 * 1024)
        
        print(f"✅ Dataset saved: {file_size_mb:.1f}MB in {save_time:.2f}s")
        
        # Save metadata
        metadata = {
            'creation_info': {
                'timestamp': timestamp,
                'target_rows': target_rows,
                'actual_rows': len(df),
                'source_files': self.source_files,
                'creation_time_seconds': save_time,
            },
            'file_info': {
                'filepath': str(filepath),
                'filename': filename,
                'size_mb': file_size_mb,
                'compression': 'snappy',
            },
            'dataset_analysis': analysis,
        }
        
        metadata_file = filepath.with_suffix('.json')
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        print(f"📋 Metadata saved to {metadata_file}")
        
        return str(filepath)

def main():
    parser = argparse.ArgumentParser(description="Create large datasets for Lance benchmarking")
    parser.add_argument("--target-rows", type=int, required=True, 
                       help="Target number of rows (e.g., 3000000 for 3M)")
    parser.add_argument("--output-dir", default="benchmarking/datasets",
                       help="Output directory for datasets")
    parser.add_argument("--validate-only", action='store_true',
                       help="Only validate source files, don't create dataset")
    
    args = parser.parse_args()
    
    print("🚀 LARGE DATASET CREATOR")
    print("=" * 50)
    print(f"Target rows: {args.target_rows:,}")
    print(f"Output directory: {args.output_dir}")
    print()
    
    creator = LargeDatasetCreator(output_dir=args.output_dir)
    
    # Validate source files
    file_info = creator.validate_source_files()
    
    if not file_info:
        print("❌ No valid source files found!")
        return 1
    
    if args.validate_only:
        print("✅ Source file validation complete")
        return 0
    
    # Check if target is achievable
    total_available = sum(info['rows'] for info in file_info.values())
    if args.target_rows > total_available:
        print(f"❌ Target rows ({args.target_rows:,}) exceeds available data ({total_available:,})")
        return 1
    
    try:
        # Create dataset
        df = creator.create_stratified_sample(args.target_rows, file_info)
        
        # Analyze quality
        analysis = creator.analyze_dataset_quality(df)
        
        # Save dataset
        output_path = creator.save_dataset(df, args.target_rows, analysis)
        
        print(f"\n🎉 SUCCESS!")
        print(f"📁 Dataset created: {output_path}")
        print(f"📊 Rows: {len(df):,}")
        print(f"💾 Size: {analysis['basic_stats']['memory_usage_mb']:.1f}MB")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error creating dataset: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
