#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# Environment Validation Script for Lance Benchmarking
# Validates isolation between custom Lance and open-source Lance environments

set -e

echo "🔍 LANCE ENVIRONMENT VALIDATION"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to test environment
test_environment() {
    local env_name=$1
    local env_path=$2
    
    print_status $BLUE "Testing $env_name environment..."
    
    # Activate environment and get info
    source $env_path
    
    # Test Python version
    PYTHON_VERSION=$(python --version 2>&1)
    print_status $GREEN "  Python: $PYTHON_VERSION"
    
    # Test Lance version and location
    LANCE_INFO=$(python -c "
import lance
import os
print(f'Version: {lance.__version__}')
print(f'Location: {lance.__file__}')
print(f'GeoPage Env: {os.environ.get(\"LANCE_GEOPAGE_ENCODING_REQUESTED\", \"unset\")}')
" 2>/dev/null || echo "ERROR: Lance not available")
    
    echo "  Lance Info:"
    echo "$LANCE_INFO" | sed 's/^/    /'
    
    # Test basic Lance functionality
    LANCE_TEST=$(python -c "
import lance
import pandas as pd
import tempfile
import os

# Create test data
df = pd.DataFrame({'x': [1, 2, 3], 'y': [4, 5, 6]})

# Test write
with tempfile.TemporaryDirectory() as tmpdir:
    dataset_path = os.path.join(tmpdir, 'test')
    dataset = lance.write_dataset(df, dataset_path)
    
    # Test read
    result = dataset.to_table().to_pandas()
    assert len(result) == 3
    print('✅ Basic Lance functionality working')
" 2>&1)
    
    if [[ $LANCE_TEST == *"✅"* ]]; then
        print_status $GREEN "  $LANCE_TEST"
    else
        print_status $RED "  ❌ Lance functionality test failed: $LANCE_TEST"
        return 1
    fi
    
    echo ""
    return 0
}

# Main validation
echo "Starting environment validation..."
echo ""

# Test custom Lance environment
print_status $YELLOW "=== CUSTOM LANCE ENVIRONMENT ==="
if test_environment "Custom Lance" "/home/<USER>/Work/terrafloww/geo-research/venv/bin/activate"; then
    CUSTOM_SUCCESS=true
else
    CUSTOM_SUCCESS=false
fi

# Test opensource Lance environment
print_status $YELLOW "=== OPENSOURCE LANCE ENVIRONMENT ==="
if test_environment "Opensource Lance" "/home/<USER>/Work/terrafloww/geo-research/open_lance_venv/bin/activate"; then
    OPENSOURCE_SUCCESS=true
else
    OPENSOURCE_SUCCESS=false
fi

# Summary
print_status $YELLOW "=== VALIDATION SUMMARY ==="
if [ "$CUSTOM_SUCCESS" = true ] && [ "$OPENSOURCE_SUCCESS" = true ]; then
    print_status $GREEN "✅ Both environments validated successfully"
    
    # Check for version differences
    CUSTOM_VERSION=$(source /home/<USER>/Work/terrafloww/geo-research/venv/bin/activate && python -c "import lance; print(lance.__version__)" 2>/dev/null)
    OPENSOURCE_VERSION=$(source /home/<USER>/Work/terrafloww/geo-research/open_lance_venv/bin/activate && python -c "import lance; print(lance.__version__)" 2>/dev/null)
    
    if [ "$CUSTOM_VERSION" = "$OPENSOURCE_VERSION" ]; then
        print_status $YELLOW "⚠️  WARNING: Same Lance versions detected ($CUSTOM_VERSION)"
        print_status $YELLOW "   This may indicate the custom build is not properly isolated"
    else
        print_status $GREEN "✅ Different Lance versions detected - good isolation"
        print_status $GREEN "   Custom: $CUSTOM_VERSION, Opensource: $OPENSOURCE_VERSION"
    fi
    
    exit 0
else
    print_status $RED "❌ Environment validation failed"
    if [ "$CUSTOM_SUCCESS" = false ]; then
        print_status $RED "   Custom Lance environment has issues"
    fi
    if [ "$OPENSOURCE_SUCCESS" = false ]; then
        print_status $RED "   Opensource Lance environment has issues"
    fi
    exit 1
fi
