#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Statistical Analysis Tool for Lance Benchmarking Results
Analyzes performance improvements and statistical significance.
"""

import json
import numpy as np
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

class BenchmarkAnalyzer:
    def __init__(self, results_dir: str = "test_results"):
        self.results_dir = Path(results_dir)
        
    def find_result_files(self, pattern: str = None) -> List[Path]:
        """Find result files matching pattern."""
        if pattern:
            return list(self.results_dir.glob(pattern))
        else:
            return list(self.results_dir.glob("uber_wkb_results_*.json"))
    
    def load_result_file(self, filepath: Path) -> Dict[str, Any]:
        """Load a single result file."""
        with open(filepath) as f:
            return json.load(f)
    
    def analyze_5m_row_results(self) -> Dict[str, Any]:
        """Analyze the 5M row test results specifically."""
        print("🔍 ANALYZING 5M ROW BENCHMARK RESULTS")
        print("=" * 60)
        
        # Find the latest 5M row results
        custom_files = list(self.results_dir.glob("uber_wkb_results_custom_*.json"))
        opensource_files = list(self.results_dir.glob("uber_wkb_results_opensource_*.json"))
        
        if not custom_files or not opensource_files:
            print("❌ Missing result files for comparison")
            return {}
        
        # Get the latest files
        custom_file = max(custom_files, key=lambda x: x.stat().st_mtime)
        opensource_file = max(opensource_files, key=lambda x: x.stat().st_mtime)
        
        print(f"📁 Custom Lance results: {custom_file.name}")
        print(f"📁 Opensource Lance results: {opensource_file.name}")
        print()
        
        # Load results
        custom_data = self.load_result_file(custom_file)
        opensource_data = self.load_result_file(opensource_file)
        
        # Analyze results
        analysis = {
            'test_info': {
                'custom_file': str(custom_file),
                'opensource_file': str(opensource_file),
                'dataset_rows': custom_data.get('data_info', {}).get('rows', 0),
                'test_timestamp': datetime.now().isoformat(),
            },
            'performance_comparison': {},
            'success_rates': {},
            'key_findings': []
        }
        
        # Dataset info comparison
        custom_rows = custom_data.get('data_info', {}).get('rows', 0)
        opensource_rows = opensource_data.get('data_info', {}).get('rows', 0)
        
        print(f"📊 DATASET COMPARISON")
        print(f"   Custom Lance: {custom_rows:,} rows")
        print(f"   Opensource Lance: {opensource_rows:,} rows")
        print()
        
        # WKB Column Access Performance
        print("🗺️  WKB COLUMN ACCESS PERFORMANCE")
        custom_wkb = custom_data.get('wkb_column_performance', {})
        opensource_wkb = opensource_data.get('wkb_column_performance', {})
        
        custom_wkb_success = custom_wkb.get('wkb_success', False)
        opensource_wkb_success = opensource_wkb.get('wkb_success', False)
        
        if custom_wkb_success and opensource_wkb_success:
            custom_time = custom_wkb.get('time_seconds', 0)
            opensource_time = opensource_wkb.get('time_seconds', 0)
            
            if opensource_time > 0:
                improvement = opensource_time / custom_time
                analysis['performance_comparison']['wkb_access'] = {
                    'custom_time': custom_time,
                    'opensource_time': opensource_time,
                    'improvement_factor': improvement,
                    'improvement_percentage': (improvement - 1) * 100
                }
                
                print(f"   ✅ Custom Lance: {custom_time:.3f}s")
                print(f"   ✅ Opensource Lance: {opensource_time:.3f}s")
                print(f"   🚀 Improvement: {improvement:.1f}x faster ({(improvement-1)*100:.1f}% faster)")
                
                if improvement >= 20:
                    analysis['key_findings'].append(f"WKB access achieved {improvement:.1f}x improvement (exceeds 20x target)")
                else:
                    analysis['key_findings'].append(f"WKB access achieved {improvement:.1f}x improvement (below 20x target)")
        
        elif custom_wkb_success and not opensource_wkb_success:
            print(f"   ✅ Custom Lance: {custom_wkb.get('time_seconds', 0):.3f}s")
            print(f"   ❌ Opensource Lance: FAILED")
            print(f"   🎯 Custom Lance succeeded where Opensource failed completely!")
            
            analysis['performance_comparison']['wkb_access'] = {
                'custom_time': custom_wkb.get('time_seconds', 0),
                'opensource_time': None,
                'improvement_factor': float('inf'),
                'improvement_percentage': float('inf'),
                'opensource_failed': True
            }
            
            analysis['key_findings'].append("Custom Lance succeeded in WKB access while Opensource Lance failed completely")
        
        print()
        
        # Spatial Query Performance
        print("🔍 SPATIAL QUERY PERFORMANCE")
        custom_queries = custom_data.get('spatial_query_performance', [])
        opensource_queries = opensource_data.get('spatial_query_performance', [])
        
        analysis['performance_comparison']['spatial_queries'] = []
        
        for i, custom_query in enumerate(custom_queries):
            query_name = custom_query.get('name', f'Query {i+1}')
            custom_time = custom_query.get('time_seconds', 0)
            custom_success = 'time_seconds' in custom_query
            
            # Find corresponding opensource query
            opensource_query = None
            if i < len(opensource_queries):
                opensource_query = opensource_queries[i]
            
            if opensource_query and 'time_seconds' in opensource_query:
                opensource_time = opensource_query.get('time_seconds', 0)
                opensource_success = True
                
                if opensource_time > 0:
                    improvement = opensource_time / custom_time
                    
                    query_analysis = {
                        'name': query_name,
                        'custom_time': custom_time,
                        'opensource_time': opensource_time,
                        'improvement_factor': improvement,
                        'improvement_percentage': (improvement - 1) * 100,
                        'selectivity': custom_query.get('selectivity', 0)
                    }
                    
                    analysis['performance_comparison']['spatial_queries'].append(query_analysis)
                    
                    print(f"   {query_name}:")
                    print(f"     ✅ Custom: {custom_time:.3f}s")
                    print(f"     ✅ Opensource: {opensource_time:.3f}s")
                    print(f"     🚀 Improvement: {improvement:.2f}x faster")
                    
                    if improvement >= 5:
                        analysis['key_findings'].append(f"{query_name} achieved {improvement:.2f}x improvement (exceeds 5x target)")
            
            elif custom_success:
                print(f"   {query_name}:")
                print(f"     ✅ Custom: {custom_time:.3f}s")
                print(f"     ❌ Opensource: FAILED")
                
                query_analysis = {
                    'name': query_name,
                    'custom_time': custom_time,
                    'opensource_time': None,
                    'improvement_factor': float('inf'),
                    'improvement_percentage': float('inf'),
                    'opensource_failed': True,
                    'selectivity': custom_query.get('selectivity', 0)
                }
                
                analysis['performance_comparison']['spatial_queries'].append(query_analysis)
                analysis['key_findings'].append(f"{query_name} succeeded with Custom Lance while Opensource failed")
        
        print()
        
        # Success Rates
        custom_spatial_success = len([q for q in custom_queries if 'time_seconds' in q])
        opensource_spatial_success = len([q for q in opensource_queries if 'time_seconds' in q])
        total_spatial_tests = len(custom_queries)
        
        analysis['success_rates'] = {
            'wkb_access': {
                'custom': custom_wkb_success,
                'opensource': opensource_wkb_success
            },
            'spatial_queries': {
                'custom': f"{custom_spatial_success}/{total_spatial_tests}",
                'opensource': f"{opensource_spatial_success}/{total_spatial_tests}",
                'custom_percentage': (custom_spatial_success / total_spatial_tests * 100) if total_spatial_tests > 0 else 0,
                'opensource_percentage': (opensource_spatial_success / total_spatial_tests * 100) if total_spatial_tests > 0 else 0
            }
        }
        
        print("📈 SUCCESS RATES")
        print(f"   WKB Access:")
        print(f"     Custom Lance: {'✅ SUCCESS' if custom_wkb_success else '❌ FAILED'}")
        print(f"     Opensource Lance: {'✅ SUCCESS' if opensource_wkb_success else '❌ FAILED'}")
        print(f"   Spatial Queries:")
        print(f"     Custom Lance: {custom_spatial_success}/{total_spatial_tests} ({analysis['success_rates']['spatial_queries']['custom_percentage']:.0f}%)")
        print(f"     Opensource Lance: {opensource_spatial_success}/{total_spatial_tests} ({analysis['success_rates']['spatial_queries']['opensource_percentage']:.0f}%)")
        print()
        
        # Key Findings Summary
        print("🎯 KEY FINDINGS")
        for i, finding in enumerate(analysis['key_findings'], 1):
            print(f"   {i}. {finding}")
        
        if not analysis['key_findings']:
            print("   No significant findings to report")
        
        print()
        
        # Overall Assessment
        print("🏆 OVERALL ASSESSMENT")
        
        if custom_wkb_success and not opensource_wkb_success:
            print("   🥇 CUSTOM LANCE: COMPLETE SUCCESS")
            print("   🔴 OPENSOURCE LANCE: COMPLETE FAILURE")
            print("   📊 Custom Lance demonstrates superior stability and performance at scale")
            
            analysis['overall_assessment'] = {
                'winner': 'custom',
                'custom_status': 'complete_success',
                'opensource_status': 'complete_failure',
                'scale_capability': 'custom_only'
            }
        
        return analysis
    
    def save_analysis(self, analysis: Dict[str, Any], output_file: str = None) -> str:
        """Save analysis results to file."""
        if not output_file:
            timestamp = int(datetime.now().timestamp())
            output_file = f"benchmarking/results/analysis_{timestamp}.json"
        
        output_path = Path(output_file)
        output_path.parent.mkdir(exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(analysis, f, indent=2, default=str)
        
        print(f"💾 Analysis saved to: {output_path}")
        return str(output_path)

def main():
    parser = argparse.ArgumentParser(description="Analyze Lance benchmarking results")
    parser.add_argument("--results-dir", default="test_results", help="Directory containing result files")
    parser.add_argument("--output", help="Output file for analysis results")
    parser.add_argument("--latest", action='store_true', help="Analyze latest 5M row results")
    
    args = parser.parse_args()
    
    analyzer = BenchmarkAnalyzer(results_dir=args.results_dir)
    
    if args.latest:
        analysis = analyzer.analyze_5m_row_results()
        if analysis:
            analyzer.save_analysis(analysis, args.output)
    else:
        print("Please specify --latest to analyze the latest 5M row results")

if __name__ == "__main__":
    main()
