{"test_info": {"custom_file": "test_results/uber_wkb_results_custom_1751202487.json", "opensource_file": "test_results/uber_wkb_results_opensource_1751202570.json", "dataset_rows": 5000000, "test_timestamp": "2025-06-29T18:40:36.687311"}, "performance_comparison": {"wkb_access": {"custom_time": 0.09725832939147949, "opensource_time": null, "improvement_factor": Infinity, "improvement_percentage": Infinity, "opensource_failed": true}, "spatial_queries": [{"name": "Manhattan Core WKB", "custom_time": 0.17292571067810059, "opensource_time": null, "improvement_factor": Infinity, "improvement_percentage": Infinity, "opensource_failed": true, "selectivity": 0.2416916}, {"name": "Small Area WKB", "custom_time": 0.07725858688354492, "opensource_time": null, "improvement_factor": Infinity, "improvement_percentage": Infinity, "opensource_failed": true, "selectivity": 0.0443422}, {"name": "Airport Area WKB", "custom_time": 0.054924964904785156, "opensource_time": null, "improvement_factor": Infinity, "improvement_percentage": Infinity, "opensource_failed": true, "selectivity": 0.0048848}]}, "success_rates": {"wkb_access": {"custom": true, "opensource": false}, "spatial_queries": {"custom": "3/3", "opensource": "0/3", "custom_percentage": 100.0, "opensource_percentage": 0.0}}, "key_findings": ["Custom Lance succeeded in WKB access while Opensource Lance failed completely", "Manhattan Core WKB succeeded with Custom Lance while Opensource failed", "Small Area WKB succeeded with Custom Lance while Opensource failed", "Airport Area WKB succeeded with Custom Lance while Opensource failed"], "overall_assessment": {"winner": "custom", "custom_status": "complete_success", "opensource_status": "complete_failure", "scale_capability": "custom_only"}}