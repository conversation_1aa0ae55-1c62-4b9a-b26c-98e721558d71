# Arrow Buffer Assertion Failure - Technical Analysis

**Date:** 2025-01-29  
**Issue:** Custom Lance spatial queries fail with Arrow buffer assertion while WKB access works perfectly  
**Error:** `assertion 'left == right' failed: PrimitiveArray data should contain a single buffer only (values buffer), left: 2, right: 1`

## 🎯 **Root Cause Identified**

### **The Problem**
Our GeoPage Z-order sorting creates `NullableDataBlock` structures that have **TWO buffers**:
1. **Data buffer** (coordinate values)  
2. **Null bitmap buffer** (validity information)

When <PERSON> converts these to Arrow arrays, it violates Arrow's expectation that `PrimitiveArray` should have **only ONE buffer** (the values buffer).

### **Why WKB Access Works (23.7x improvement)**
- **Data Type**: WKB columns use `VariableWidthBlock` (binary data)
- **Buffer Structure**: Uses offsets + data buffers (valid for binary arrays)
- **Processing**: Direct binary access, no coordinate processing
- **Result**: ✅ **0.077s** vs 1.825s (23.7x faster)

### **Why Spatial Queries Fail**
- **Data Type**: Coordinate columns use `FixedWidthDataBlock` (Float32)
- **Processing**: Goes through Z-order sorting with null handling
- **Buffer Creation**: Creates `NullableDataBlock` with data + null buffers
- **Result**: ❌ **Arrow assertion failure**

## 🔍 **Exact Code Location**

### **File:** `lance-internal/rust/lance-encoding/src/encodings/geopage.rs`
### **Function:** `apply_z_order_sorting_with_mapping()` 
### **Lines:** 710-735

```rust
DataBlock::Nullable(ref nullable) => {
    // Handle nullable data blocks by sorting the inner data and preserving null bitmap
    let sorted_inner = match nullable.data.as_ref() {
        DataBlock::VariableWidth(vw) => {
            let sorted_vw = self.reorder_variable_width_data(&vw, &morton_indices)?;
            DataBlock::VariableWidth(sorted_vw)
        }
        DataBlock::FixedWidth(fw) => {
            let sorted_fw = self.reorder_fixed_width_data(&fw, &morton_indices)?;
            DataBlock::FixedWidth(sorted_fw)  // ✅ This is fine
        }
        _ => return Ok((data, sort_mapping)),
    };

    // ❌ PROBLEM: Always creates NullableDataBlock even when no nulls exist
    let sorted_nulls = self.reorder_null_bitmap(&nullable.nulls, &morton_indices)?;

    DataBlock::Nullable(crate::data::NullableDataBlock {
        data: Box::new(sorted_inner),
        nulls: sorted_nulls,  // ❌ EXTRA BUFFER - causes Arrow assertion
        block_info: nullable.block_info.clone(),
    })
}
```

### **Arrow Conversion Issue**

**File:** `lance-internal/rust/lance-encoding/src/data.rs`  
**Function:** `NullableDataBlock::into_arrow()`  
**Lines:** 113-122

```rust
impl NullableDataBlock {
    fn into_arrow(self, data_type: DataType, validate: bool) -> Result<ArrayData> {
        let nulls = self.nulls.into_buffer();  // ❌ Buffer 1: null bitmap
        let data = self.data.into_arrow(data_type, validate)?.into_builder();
        let data = data.null_bit_buffer(Some(nulls));  // ❌ Buffer 2: data values
        // Arrow expects PrimitiveArray to have only ONE buffer!
    }
}
```

## 🚀 **Fix Strategy**

### **Solution: Smart Null Handling**
Only create `NullableDataBlock` when there are **actual null values** that need preservation.

### **Implementation Steps**

#### **Step 1: Add Null Detection Function**
```rust
/// Check if a null bitmap contains any actual null values
fn has_actual_nulls(&self, nulls: &crate::buffer::LanceBuffer, num_values: usize) -> bool {
    let null_bytes = nulls.as_ref();
    let expected_set_bits = num_values;
    let mut actual_set_bits = 0;
    
    for (byte_idx, &byte) in null_bytes.iter().enumerate() {
        let bits_in_this_byte = std::cmp::min(8, num_values - byte_idx * 8);
        actual_set_bits += byte.count_ones() as usize;
        
        if byte_idx * 8 + bits_in_this_byte >= num_values {
            break;
        }
    }
    
    actual_set_bits < expected_set_bits
}
```

#### **Step 2: Modify Z-order Sorting Logic**
```rust
DataBlock::Nullable(ref nullable) => {
    let sorted_inner = match nullable.data.as_ref() {
        // ... existing sorting logic ...
    };

    // ✅ NEW: Only create NullableDataBlock if there are actual nulls
    if self.has_actual_nulls(&nullable.nulls, morton_indices.len()) {
        let sorted_nulls = self.reorder_null_bitmap(&nullable.nulls, &morton_indices)?;
        DataBlock::Nullable(crate::data::NullableDataBlock {
            data: Box::new(sorted_inner),
            nulls: sorted_nulls,
            block_info: nullable.block_info.clone(),
        })
    } else {
        // ✅ No nulls - return sorted data directly (avoids buffer issue)
        sorted_inner
    }
}
```

## 📊 **Expected Results**

### **Before Fix**
- ✅ WKB Access: 0.077s (23.7x improvement)
- ❌ Spatial Queries: Arrow assertion failure
- ❌ Success Rate: 25% (1/4 tests pass)

### **After Fix**  
- ✅ WKB Access: 0.077s (maintains 23.7x improvement)
- ✅ Spatial Queries: Expected to work with performance gains
- ✅ Success Rate: 100% (4/4 tests pass)

## 🔧 **Implementation Priority**

### **High Priority (Immediate)**
1. ✅ **Root cause identified** - Arrow buffer assertion in NullableDataBlock
2. 🔄 **Fix implementation** - Smart null handling in Z-order sorting
3. 🔄 **Testing** - Verify fix maintains WKB performance gains

### **Medium Priority (Follow-up)**
1. **Performance optimization** - Further spatial query improvements
2. **Edge case handling** - Mixed null/non-null scenarios
3. **Documentation** - Update GeoPage implementation guide

## 🎯 **Success Criteria**

1. ✅ **Maintain WKB Performance**: 23.7x improvement preserved
2. ✅ **Fix Spatial Queries**: All 3 spatial queries succeed
3. ✅ **No Regressions**: Existing functionality unaffected
4. ✅ **Clean Implementation**: Follows Lance/Arrow best practices

## 📝 **Technical Notes**

- **Arrow Requirement**: PrimitiveArray must have exactly ONE buffer (values only)
- **Lance Pattern**: NullableDataBlock adds null bitmap as separate buffer
- **GeoPage Impact**: Z-order sorting preserves null information unnecessarily
- **Solution Approach**: Conditional null handling based on actual null presence

This fix will resolve the Arrow buffer assertion while maintaining all performance benefits of our GeoPage encoding implementation.
