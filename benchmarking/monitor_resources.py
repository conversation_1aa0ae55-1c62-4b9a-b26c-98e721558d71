#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Resource Monitoring Utility for Lance Benchmarking
Monitors system resources during testing to ensure safe operation.
"""

import psutil
import time
import json
import argparse
import signal
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class ResourceMonitor:
    def __init__(self, output_dir: str = "benchmarking/logs"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.measurements = []
        self.running = False
        
        # Thresholds (from benchmarking plan)
        self.memory_warning_threshold = 60  # % of total RAM
        self.memory_stop_threshold = 80     # % of total RAM
        self.disk_warning_threshold = 85    # % of disk usage
        self.disk_stop_threshold = 90       # % of disk usage
        
    def get_current_measurement(self) -> Dict[str, Any]:
        """Get current system resource measurement."""
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/home/<USER>/Work/terrafloww/geo-research')
        
        measurement = {
            'timestamp': time.time(),
            'datetime': datetime.now().isoformat(),
            'memory': {
                'total_gb': memory.total / (1024**3),
                'available_gb': memory.available / (1024**3),
                'used_gb': memory.used / (1024**3),
                'percent_used': memory.percent,
            },
            'disk': {
                'total_gb': disk.total / (1024**3),
                'free_gb': disk.free / (1024**3),
                'used_gb': disk.used / (1024**3),
                'percent_used': (disk.used / disk.total) * 100,
            },
            'cpu_percent': psutil.cpu_percent(interval=1),
            'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0],
        }
        
        return measurement
    
    def check_thresholds(self, measurement: Dict[str, Any]) -> Dict[str, str]:
        """Check if any thresholds are exceeded."""
        warnings = {}
        
        # Memory checks
        memory_percent = measurement['memory']['percent_used']
        if memory_percent >= self.memory_stop_threshold:
            warnings['memory'] = f"STOP: Memory usage {memory_percent:.1f}% >= {self.memory_stop_threshold}%"
        elif memory_percent >= self.memory_warning_threshold:
            warnings['memory'] = f"WARNING: Memory usage {memory_percent:.1f}% >= {self.memory_warning_threshold}%"
        
        # Disk checks
        disk_percent = measurement['disk']['percent_used']
        if disk_percent >= self.disk_stop_threshold:
            warnings['disk'] = f"STOP: Disk usage {disk_percent:.1f}% >= {self.disk_stop_threshold}%"
        elif disk_percent >= self.disk_warning_threshold:
            warnings['disk'] = f"WARNING: Disk usage {disk_percent:.1f}% >= {self.disk_warning_threshold}%"
        
        return warnings
    
    def print_measurement(self, measurement: Dict[str, Any], warnings: Dict[str, str] = None):
        """Print current measurement in a readable format."""
        mem = measurement['memory']
        disk = measurement['disk']
        
        print(f"\r🔍 Resources: "
              f"RAM {mem['percent_used']:.1f}% ({mem['available_gb']:.1f}GB free), "
              f"Disk {disk['percent_used']:.1f}% ({disk['free_gb']:.1f}GB free), "
              f"CPU {measurement['cpu_percent']:.1f}%", end="", flush=True)
        
        if warnings:
            print()  # New line for warnings
            for warning_type, message in warnings.items():
                if "STOP" in message:
                    print(f"🛑 {message}")
                else:
                    print(f"⚠️  {message}")
    
    def monitor_continuous(self, interval: int = 5, duration: int = None):
        """Monitor resources continuously."""
        print(f"🔍 Starting resource monitoring (interval: {interval}s)")
        if duration:
            print(f"   Duration: {duration}s")
        print("   Press Ctrl+C to stop")
        print()
        
        self.running = True
        start_time = time.time()
        
        # Set up signal handler for graceful shutdown
        def signal_handler(sig, frame):
            print("\n🛑 Stopping resource monitoring...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        
        try:
            while self.running:
                measurement = self.get_current_measurement()
                warnings = self.check_thresholds(measurement)
                
                self.measurements.append(measurement)
                self.print_measurement(measurement, warnings)
                
                # Check for stop conditions
                if any("STOP" in warning for warning in warnings.values()):
                    print(f"\n🛑 CRITICAL: Stop condition reached!")
                    break
                
                # Check duration
                if duration and (time.time() - start_time) >= duration:
                    print(f"\n✅ Monitoring duration completed")
                    break
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        
        self.running = False
        return self.save_measurements()
    
    def monitor_single(self) -> Dict[str, Any]:
        """Take a single measurement."""
        measurement = self.get_current_measurement()
        warnings = self.check_thresholds(measurement)
        
        self.print_measurement(measurement, warnings)
        print()  # New line after measurement
        
        if warnings:
            for warning_type, message in warnings.items():
                if "STOP" in message:
                    print(f"🛑 {message}")
                    return {'status': 'stop', 'measurement': measurement, 'warnings': warnings}
                else:
                    print(f"⚠️  {message}")
        
        return {'status': 'ok', 'measurement': measurement, 'warnings': warnings}
    
    def save_measurements(self) -> str:
        """Save measurements to file."""
        if not self.measurements:
            return None
            
        timestamp = int(time.time())
        filename = f"resource_monitoring_{timestamp}.json"
        filepath = self.output_dir / filename
        
        data = {
            'monitoring_session': {
                'start_time': self.measurements[0]['timestamp'],
                'end_time': self.measurements[-1]['timestamp'],
                'duration_seconds': self.measurements[-1]['timestamp'] - self.measurements[0]['timestamp'],
                'measurement_count': len(self.measurements),
            },
            'thresholds': {
                'memory_warning': self.memory_warning_threshold,
                'memory_stop': self.memory_stop_threshold,
                'disk_warning': self.disk_warning_threshold,
                'disk_stop': self.disk_stop_threshold,
            },
            'measurements': self.measurements
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        
        print(f"\n💾 Resource monitoring data saved to: {filepath}")
        return str(filepath)

def main():
    parser = argparse.ArgumentParser(description="Monitor system resources during Lance benchmarking")
    parser.add_argument("--interval", type=int, default=5, help="Monitoring interval in seconds")
    parser.add_argument("--duration", type=int, help="Monitoring duration in seconds (continuous if not specified)")
    parser.add_argument("--single", action='store_true', help="Take a single measurement and exit")
    parser.add_argument("--output-dir", default="benchmarking/logs", help="Output directory for logs")
    
    args = parser.parse_args()
    
    monitor = ResourceMonitor(output_dir=args.output_dir)
    
    if args.single:
        result = monitor.monitor_single()
        if result['status'] == 'stop':
            print("🛑 Critical resource usage detected!")
            sys.exit(1)
    else:
        monitor.monitor_continuous(interval=args.interval, duration=args.duration)

if __name__ == "__main__":
    main()
