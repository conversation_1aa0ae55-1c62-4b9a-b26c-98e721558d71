#!/usr/bin/env python3
"""
Comprehensive WKB A/B Testing Suite
Runs complete A/B tests comparing open-source Lance vs custom Lance with GeoPage
using real Uber data converted to WKB Point geometries.

This combines the WKB integration testing with comprehensive A/B performance analysis.
"""

import os
import sys
import time
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Any

class ComprehensiveWKBABTester:
    def __init__(self):
        self.results = {}
        self.test_configs = [
            {
                'mode': 'opensource',
                'description': 'Open-source Lance with WKB data',
                'env_setup': self.setup_opensource_env,
            },
            {
                'mode': 'custom',
                'description': 'Custom Lance with GeoPage + WKB data',
                'env_setup': self.setup_custom_env,
            },
            {
                'mode': 'presorted',
                'description': 'Standard Lance with Z-order pre-sorted WKB data',
                'env_setup': self.setup_opensource_env,
            }
        ]
        
    def setup_opensource_env(self):
        """Setup open-source Lance environment."""
        print("🔧 Setting up open-source Lance environment...")
        # Switch to open-source Lance environment
        os.environ.pop('LANCE_GEOPAGE_ENCODING_REQUESTED', None)
        return True
        
    def setup_custom_env(self):
        """Setup custom Lance with GeoPage environment."""
        print("🔧 Setting up custom Lance with GeoPage environment...")
        # Ensure we're using the custom Lance build
        os.environ['LANCE_GEOPAGE_ENCODING_REQUESTED'] = '1'
        return True
        
    def drop_caches(self):
        """Drop OS page caches for fair benchmarking."""
        try:
            result = subprocess.run(
                ["sudo", "sh", "-c", "echo 3 > /proc/sys/vm/drop_caches"],
                capture_output=True, text=True, timeout=5
            )
            if result.returncode == 0:
                print("✅ Dropped OS page caches")
                return True
            else:
                print("🟡 Could not drop caches (no sudo)")
                return False
        except:
            print("🟡 Could not drop caches")
            return False
            
    def run_single_test(self, config: Dict[str, Any], data_file: str, max_rows: int) -> Dict[str, Any]:
        """Run a single WKB test configuration."""
        mode = config['mode']
        print(f"\n{'='*80}")
        print(f"🚀 RUNNING {mode.upper()} WKB TEST")
        print(f"📝 {config['description']}")
        print(f"{'='*80}")
        
        # Setup environment
        if not config['env_setup']():
            return {'error': 'Environment setup failed'}
            
        # Drop caches for fair comparison
        self.drop_caches()
        
        # Run the WKB integration test with proper environment
        if mode == 'opensource' or mode == 'presorted':
            # Use open source Lance environment
            cmd = ["bash", "-c", f"source /home/<USER>/Work/terrafloww/geo-research/open_lance_venv/bin/activate && python test_uber_wkb_integration.py --mode {mode} --data-file {data_file} --max-rows {max_rows}"]
        else:
            # Use custom Lance environment (current venv)
            cmd = ["bash", "-c", f"source /home/<USER>/Work/terrafloww/geo-research/venv/bin/activate && python test_uber_wkb_integration.py --mode {mode} --data-file {data_file} --max-rows {max_rows}"]
        
        try:
            print(f"🔧 Running: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            print("✅ Test completed successfully")
            if result.stdout:
                print("STDOUT:", result.stdout[-1000:])  # Last 1000 chars
            
            # Try to load the results file
            results_dir = Path("test_results")
            result_files = sorted(results_dir.glob(f"uber_wkb_results_{mode}_*.json"))
            
            if result_files:
                with open(result_files[-1]) as f:
                    test_results = json.load(f)
                return test_results
            else:
                return {'error': 'No results file found'}
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Test failed: {e}")
            print("STDOUT:", e.stdout)
            print("STDERR:", e.stderr)
            return {'error': str(e), 'stdout': e.stdout, 'stderr': e.stderr}
            
    def compare_results(self, results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Compare results across all test modes."""
        print(f"\n{'='*80}")
        print("📊 COMPREHENSIVE WKB A/B COMPARISON")
        print(f"{'='*80}")
        
        comparison = {
            'summary': {},
            'detailed_comparison': {},
            'performance_analysis': {},
            'recommendations': []
        }
        
        # Extract key metrics for comparison
        modes = list(results.keys())
        valid_modes = [mode for mode in modes if 'error' not in results[mode]]
        
        if len(valid_modes) < 2:
            print("❌ Need at least 2 successful tests for comparison")
            return comparison
            
        print(f"📊 Comparing {len(valid_modes)} successful test modes:")
        for mode in valid_modes:
            print(f"  ✅ {mode}")
            
        # Compare write performance
        print(f"\n📝 WRITE PERFORMANCE COMPARISON")
        print(f"{'Mode':<15} {'Time (s)':<10} {'Size (MB)':<12} {'Compression':<12} {'Success':<8}")
        print("-" * 65)
        
        write_comparison = {}
        for mode in valid_modes:
            write_perf = results[mode].get('write_performance', {})
            if write_perf.get('write_success'):
                time_s = write_perf.get('time_seconds', 0)
                size_mb = write_perf.get('dataset_size_mb', 0)
                compression = write_perf.get('compression_ratio', 0)
                
                write_comparison[mode] = {
                    'time_seconds': time_s,
                    'size_mb': size_mb,
                    'compression_ratio': compression
                }
                
                print(f"{mode:<15} {time_s:<10.3f} {size_mb:<12.2f} {compression:<12.2f} {'✅':<8}")
            else:
                print(f"{mode:<15} {'FAILED':<10} {'FAILED':<12} {'FAILED':<12} {'❌':<8}")
        
        # Calculate improvements
        if 'opensource' in write_comparison and 'custom' in write_comparison:
            os_time = write_comparison['opensource']['time_seconds']
            custom_time = write_comparison['custom']['time_seconds']
            time_improvement = ((os_time - custom_time) / os_time) * 100
            
            os_size = write_comparison['opensource']['size_mb']
            custom_size = write_comparison['custom']['size_mb']
            size_change = ((custom_size - os_size) / os_size) * 100
            
            print(f"\n🎯 GeoPage vs Open Source:")
            print(f"  ⏱️ Write time: {time_improvement:+.1f}% ({'faster' if time_improvement > 0 else 'slower'})")
            print(f"  💾 Dataset size: {size_change:+.1f}% ({'larger' if size_change > 0 else 'smaller'})")
            
        # Compare WKB column access performance
        print(f"\n🗺️ WKB COLUMN ACCESS COMPARISON")
        print(f"{'Mode':<15} {'Time (s)':<10} {'Pickup WKB':<12} {'Dropoff WKB':<12} {'Success':<8}")
        print("-" * 65)
        
        for mode in valid_modes:
            wkb_perf = results[mode].get('wkb_column_performance', {})
            if wkb_perf.get('wkb_success'):
                time_s = wkb_perf.get('time_seconds', 0)
                pickup_count = wkb_perf.get('pickup_wkb_count', 0)
                dropoff_count = wkb_perf.get('dropoff_wkb_count', 0)
                
                print(f"{mode:<15} {time_s:<10.3f} {pickup_count:<12} {dropoff_count:<12} {'✅':<8}")
            else:
                print(f"{mode:<15} {'FAILED':<10} {'FAILED':<12} {'FAILED':<12} {'❌':<8}")
        
        # Compare spatial query performance
        print(f"\n🔍 SPATIAL QUERY PERFORMANCE COMPARISON")
        
        # Get common queries across all modes
        common_queries = set()
        for mode in valid_modes:
            queries = results[mode].get('spatial_query_performance', [])
            query_names = {q['name'] for q in queries if 'error' not in q}
            if not common_queries:
                common_queries = query_names
            else:
                common_queries &= query_names
        
        if common_queries:
            print(f"{'Query':<25} {'Open Source':<12} {'Custom':<12} {'Presorted':<12} {'Best':<8}")
            print("-" * 75)
            
            for query_name in sorted(common_queries):
                query_times = {}
                for mode in valid_modes:
                    queries = results[mode].get('spatial_query_performance', [])
                    for query in queries:
                        if query.get('name') == query_name and 'error' not in query:
                            query_times[mode] = query.get('time_seconds', float('inf'))
                
                # Print comparison
                os_time = query_times.get('opensource', float('inf'))
                custom_time = query_times.get('custom', float('inf'))
                presorted_time = query_times.get('presorted', float('inf'))
                
                best_mode = min(query_times.keys(), key=lambda k: query_times[k]) if query_times else 'none'
                
                print(f"{query_name:<25} {os_time if os_time != float('inf') else 'N/A':<12} "
                      f"{custom_time if custom_time != float('inf') else 'N/A':<12} "
                      f"{presorted_time if presorted_time != float('inf') else 'N/A':<12} {best_mode:<8}")
        
        # Generate recommendations
        recommendations = []
        
        if 'custom' in write_comparison and 'opensource' in write_comparison:
            custom_faster = write_comparison['custom']['time_seconds'] < write_comparison['opensource']['time_seconds']
            if custom_faster:
                recommendations.append("✅ GeoPage encoding provides faster write performance")
            else:
                recommendations.append("⚠️ GeoPage encoding has slower write performance - investigate optimization")
        
        if common_queries:
            # Analyze spatial query performance
            custom_wins = 0
            total_comparisons = 0
            
            for query_name in common_queries:
                for mode in valid_modes:
                    queries = results[mode].get('spatial_query_performance', [])
                    for query in queries:
                        if query.get('name') == query_name and 'error' not in query:
                            if mode == 'custom':
                                custom_time = query.get('time_seconds', float('inf'))
                            elif mode == 'opensource':
                                os_time = query.get('time_seconds', float('inf'))
                
                if 'custom_time' in locals() and 'os_time' in locals():
                    total_comparisons += 1
                    if custom_time < os_time:
                        custom_wins += 1
            
            if total_comparisons > 0:
                win_rate = custom_wins / total_comparisons
                if win_rate > 0.7:
                    recommendations.append("✅ GeoPage provides significant spatial query performance benefits")
                elif win_rate > 0.5:
                    recommendations.append("🟡 GeoPage provides moderate spatial query performance benefits")
                else:
                    recommendations.append("⚠️ GeoPage spatial query performance needs optimization")
        
        comparison['recommendations'] = recommendations
        
        print(f"\n🎯 RECOMMENDATIONS:")
        for rec in recommendations:
            print(f"  {rec}")
            
        return comparison
        
    def save_comparison_results(self, results: Dict[str, Any], comparison: Dict[str, Any]):
        """Save comprehensive comparison results."""
        output_dir = Path("test_results")
        output_dir.mkdir(exist_ok=True)
        
        timestamp = int(time.time())
        filename = f"comprehensive_wkb_ab_comparison_{timestamp}.json"
        filepath = output_dir / filename
        
        full_results = {
            'timestamp': timestamp,
            'test_type': 'comprehensive_wkb_ab_testing',
            'individual_results': results,
            'comparison_analysis': comparison,
        }
        
        with open(filepath, 'w') as f:
            json.dump(full_results, f, indent=2, default=str)
            
        print(f"\n💾 Comprehensive results saved to: {filepath}")
        return filepath

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Comprehensive WKB A/B testing suite")
    parser.add_argument("--data-file", type=str,
                       default="uber_data/nyc-taxi/2009/01/data.parquet",
                       help="Path to Uber data parquet file")
    parser.add_argument("--max-rows", type=int, default=100000,
                       help="Maximum rows to test")
    parser.add_argument("--modes", nargs='+', 
                       choices=['opensource', 'custom', 'presorted', 'all'],
                       default=['all'],
                       help="Test modes to run")
    
    args = parser.parse_args()
    
    print("🚗" * 20)
    print("🚀 COMPREHENSIVE WKB A/B TESTING SUITE")
    print("🚗" * 20)
    print(f"📁 Data file: {args.data_file}")
    print(f"📊 Max rows: {args.max_rows:,}")
    
    if not os.path.exists(args.data_file):
        print(f"❌ Data file not found: {args.data_file}")
        sys.exit(1)
    
    tester = ComprehensiveWKBABTester()
    
    # Determine which modes to test
    if 'all' in args.modes:
        modes_to_test = ['opensource', 'custom', 'presorted']
    else:
        modes_to_test = args.modes
    
    print(f"🧪 Testing modes: {', '.join(modes_to_test)}")
    
    # Run tests for each mode
    results = {}
    for config in tester.test_configs:
        if config['mode'] in modes_to_test:
            results[config['mode']] = tester.run_single_test(config, args.data_file, args.max_rows)
    
    # Compare results
    comparison = tester.compare_results(results)
    
    # Save comprehensive results
    results_file = tester.save_comparison_results(results, comparison)
    
    print(f"\n🎉 Comprehensive WKB A/B testing complete!")
    print(f"📊 Results saved to: {results_file}")
    
    return results, comparison

if __name__ == "__main__":
    results, comparison = main()
