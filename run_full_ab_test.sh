#!/bin/bash

echo "🧪 Full A/B Test: Custom Lance vs Open-source Lance"
echo "=================================================="

# Test 1: Custom Lance (current venv with GeoPage optimizations)
echo ""
echo "🔧 Phase 1: Testing Custom Lance with GeoPage optimizations"
echo "Environment: venv (custom Lance build)"
echo "-----------------------------------------------------------"

# Already in venv, run the GeoPage test
python geopage_ab_test.py

echo ""
echo "🔄 Switching to open-source Lance environment..."
echo ""

# Test 2: Open-source Lance baseline
echo "📦 Phase 2: Testing Open-source Lance baseline"
echo "Environment: open_lance_venv (open-source Lance)"
echo "------------------------------------------------"

# Switch to open-source Lance environment and run baseline test
source ../open_lance_venv/bin/activate
python run_baseline_test.py

# Switch back to custom environment
echo ""
echo "🔄 Switching back to custom Lance environment..."
source ../venv/bin/activate

echo ""
echo "📊 Creating comparison analysis..."

# Create comparison script
cat > compare_results.py << 'EOF'
#!/usr/bin/env python3
"""
Compare GeoPage A/B test results with baseline results
"""

import pandas as pd
import numpy as np

def compare_results():
    print("📊 A/B Test Results Comparison")
    print("=" * 50)
    
    try:
        # Load results
        geopage_df = pd.read_csv('geopage_ab_test_results.csv')
        baseline_df = pd.read_csv('baseline_test_results.csv')
        
        print(f"✅ Loaded GeoPage results: {len(geopage_df)} records")
        print(f"✅ Loaded Baseline results: {len(baseline_df)} records")
        
        # Filter to only custom GeoPage results for comparison
        custom_df = geopage_df[geopage_df['lance_version'] == 'custom_geopage']
        
        print("\n🚀 Performance Comparison Summary:")
        print("-" * 40)
        
        for size in sorted(custom_df['dataset_size'].unique()):
            custom_avg = custom_df[custom_df['dataset_size'] == size]['query_time_ms'].mean()
            baseline_avg = baseline_df[baseline_df['dataset_size'] == size]['query_time_ms'].mean()
            
            improvement = ((baseline_avg - custom_avg) / baseline_avg) * 100
            speedup = baseline_avg / custom_avg
            
            print(f"📈 {size:,} rows:")
            print(f"   Custom Lance:     {custom_avg:.1f}ms")
            print(f"   Open-source:      {baseline_avg:.1f}ms")
            print(f"   Improvement:      {improvement:.1f}% faster")
            print(f"   Speedup:          {speedup:.2f}x")
            print()
        
        # Overall statistics
        custom_overall = custom_df['query_time_ms'].mean()
        baseline_overall = baseline_df['query_time_ms'].mean()
        overall_improvement = ((baseline_overall - custom_overall) / baseline_overall) * 100
        
        print(f"🎯 Overall Performance:")
        print(f"   Custom Lance avg:   {custom_overall:.1f}ms")
        print(f"   Open-source avg:    {baseline_overall:.1f}ms")
        print(f"   Overall improvement: {overall_improvement:.1f}% faster")
        
        # Spatial selectivity analysis
        print(f"\n🗺️  Spatial Query Analysis:")
        print("-" * 30)
        
        # Group by bbox area to see spatial indexing benefits
        custom_spatial = custom_df.groupby('bbox_area')['query_time_ms'].mean()
        baseline_spatial = baseline_df.groupby('bbox_area')['query_time_ms'].mean()
        
        for area in sorted(custom_spatial.index):
            if area in baseline_spatial.index:
                custom_time = custom_spatial[area]
                baseline_time = baseline_spatial[area]
                improvement = ((baseline_time - custom_time) / baseline_time) * 100
                
                print(f"   Bbox area {area:.4f}: {improvement:.1f}% faster")
        
        print(f"\n✅ GeoPage optimizations are working!")
        print(f"💡 Smaller spatial queries show the biggest improvements")
        
    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
        print("Make sure both test files have been run successfully")

if __name__ == "__main__":
    compare_results()
EOF

python compare_results.py

echo ""
echo "✅ Full A/B Test Complete!"
echo ""
echo "📁 Generated files:"
echo "   - geopage_ab_test_results.csv (custom Lance results)"
echo "   - baseline_test_results.csv (open-source Lance results)"
echo "   - compare_results.py (comparison analysis)"
