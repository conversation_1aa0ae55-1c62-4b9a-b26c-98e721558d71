#!/usr/bin/env python3
"""
Simple environment check to verify both Lance environments work.
This script tests basic Lance functionality before running A/B tests.
"""

import sys
import os
import pandas as pd
import pyarrow as pa
import lance
import tempfile
import shutil
from pathlib import Path

def test_basic_lance_functionality():
    """Test basic Lance read/write functionality."""
    print("🧪 Testing basic Lance functionality...")
    
    # Create test data
    data = pd.DataFrame({
        'id': range(100),
        'longitude': [-122.4 + i * 0.001 for i in range(100)],
        'latitude': [37.7 + i * 0.001 for i in range(100)],
        'value': [i * 10 for i in range(100)]
    })
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        dataset_path = Path(temp_dir) / "test_dataset"
        
        try:
            # Write dataset
            print(f"  📝 Writing dataset to {dataset_path}")
            dataset = lance.write_dataset(data, dataset_path)
            print(f"  ✅ Dataset written successfully")
            
            # Read dataset back
            print(f"  📖 Reading dataset back")
            loaded_dataset = lance.dataset(dataset_path)
            loaded_data = loaded_dataset.to_table().to_pandas()
            print(f"  ✅ Dataset read successfully: {len(loaded_data)} rows")
            
            # Test spatial query
            print(f"  🗺️ Testing spatial query")
            spatial_filter = "longitude >= -122.4 AND longitude <= -122.39 AND latitude >= 37.7 AND latitude <= 37.71"
            filtered_data = loaded_dataset.to_table(filter=spatial_filter).to_pandas()
            print(f"  ✅ Spatial query successful: {len(filtered_data)} rows returned")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            return False

def main():
    """Main test function."""
    print("=" * 60)
    print("🔍 LANCE ENVIRONMENT CHECK")
    print("=" * 60)
    
    # Check Python environment
    print(f"🐍 Python: {sys.version}")
    print(f"📁 Working directory: {os.getcwd()}")
    
    # Check Lance version and location
    try:
        print(f"🚀 Lance version: {lance.__version__}")
        print(f"📦 Lance location: {lance.__file__}")
    except AttributeError:
        print(f"📦 Lance location: {lance.__path__}")
    
    # Check available Lance functions
    lance_functions = [attr for attr in dir(lance) if not attr.startswith('_')]
    print(f"🔧 Available Lance functions: {lance_functions[:10]}...")
    
    # Test basic functionality
    success = test_basic_lance_functionality()
    
    print("=" * 60)
    if success:
        print("✅ ENVIRONMENT CHECK PASSED - Lance is working correctly!")
    else:
        print("❌ ENVIRONMENT CHECK FAILED - Lance has issues!")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
