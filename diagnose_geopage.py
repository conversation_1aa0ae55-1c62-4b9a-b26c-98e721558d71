#!/usr/bin/env python3
"""
Diagnostic script to understand GeoPage encoding behavior
"""

import pandas as pd
import lance
import tempfile
import os
from pathlib import Path

def test_geopage_encoding():
    """Test if GeoPage encoding is actually being triggered"""
    print("🔍 Diagnosing GeoPage Encoding Behavior")
    print("=" * 50)
    
    # Load test data
    print("📊 Loading Uber data...")
    df = pd.read_parquet("uber_data/nyc-taxi/2009/01/data.parquet").head(500000)
    print(f"✅ Loaded {len(df):,} rows")
    
    # Test different encoding approaches
    with tempfile.TemporaryDirectory() as temp_dir:
        
        # Test 1: Standard Lance
        print("\n🔧 Test 1: Standard Lance encoding")
        dataset_path1 = Path(temp_dir) / "standard"
        dataset1 = lance.write_dataset(df, dataset_path1)
        print(f"✅ Standard dataset created")
        
        # Test 2: Try explicit GeoPage encoding
        print("\n🔧 Test 2: Explicit GeoPage encoding attempt")
        dataset_path2 = Path(temp_dir) / "geopage"
        try:
            # Try different ways to trigger GeoPage
            dataset2 = lance.write_dataset(
                df, 
                dataset_path2,
                data_storage_version="2.0",
                encoding="geopage"
            )
            print(f"✅ GeoPage dataset created")
        except Exception as e:
            print(f"❌ GeoPage encoding failed: {e}")
            
            # Fallback: try with field metadata
            try:
                # Add metadata to spatial columns
                import pyarrow as pa
                schema = pa.Table.from_pandas(df).schema
                
                # Add GeoPage metadata to spatial columns
                new_fields = []
                for field in schema:
                    if 'longitude' in field.name.lower() or 'latitude' in field.name.lower():
                        metadata = {
                            'encoding': 'geopage',
                            'geopage.min_rows': '100000',
                            'geopage.min_pages': '2',
                            'geopage.zoom_level': '12'
                        }
                        new_field = field.with_metadata(metadata)
                        new_fields.append(new_field)
                    else:
                        new_fields.append(field)
                
                new_schema = pa.schema(new_fields)
                table_with_metadata = pa.Table.from_pandas(df, schema=new_schema)
                
                dataset2 = lance.write_dataset(table_with_metadata, dataset_path2)
                print(f"✅ GeoPage dataset created with field metadata")
                
            except Exception as e2:
                print(f"❌ GeoPage with metadata failed: {e2}")
                dataset2 = None
        
        # Test 3: Check dataset properties
        print("\n📊 Comparing dataset properties:")
        
        # Standard dataset
        size1 = sum(f.stat().st_size for f in dataset_path1.rglob('*') if f.is_file())
        print(f"Standard dataset: {size1 / (1024*1024):.2f} MB")
        
        if dataset2:
            size2 = sum(f.stat().st_size for f in dataset_path2.rglob('*') if f.is_file())
            print(f"GeoPage dataset:  {size2 / (1024*1024):.2f} MB")
            print(f"Size difference:  {((size2 - size1) / size1) * 100:+.1f}%")
        
        # Test 4: Query performance comparison
        print("\n⚡ Testing query performance:")
        
        # Standard query
        import time
        start = time.time()
        result1 = dataset1.to_table(
            filter="pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.75 AND pickup_latitude <= 40.77"
        ).to_pandas()
        time1 = time.time() - start
        print(f"Standard query: {time1:.3f}s, {len(result1):,} rows")
        
        if dataset2:
            start = time.time()
            result2 = dataset2.to_table(
                filter="pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.75 AND pickup_latitude <= 40.77"
            ).to_pandas()
            time2 = time.time() - start
            print(f"GeoPage query:  {time2:.3f}s, {len(result2):,} rows")
            
            improvement = ((time1 - time2) / time1) * 100
            print(f"Performance:    {improvement:+.1f}% change")
        
        # Test 5: Check if our custom Lance is actually being used
        print(f"\n🔍 Lance version info:")
        print(f"Lance version: {lance.__version__}")
        print(f"Lance location: {lance.__file__}")
        
        # Check if we can access any GeoPage-specific functionality
        try:
            # Try to access any custom GeoPage functions
            print("🔍 Checking for GeoPage functionality...")
            # This would fail if GeoPage isn't properly integrated
            
        except Exception as e:
            print(f"⚠️  GeoPage functionality check: {e}")

def main():
    test_geopage_encoding()

if __name__ == "__main__":
    main()
