#!/usr/bin/env python3
"""
Comprehensive WKB Integration Test Runner
Combines synthetic WKB geometry tests with real Uber data WKB integration testing.
Based on the proven A/B testing framework from UBER_AB_ANALYSIS.md
"""

import subprocess
import sys
import json
import time
import os
from pathlib import Path
from typing import Dict, List, Any

class ComprehensiveWKBTestRunner:
    def __init__(self):
        self.results = {}
        self.test_results_dir = Path("test_results")
        self.test_results_dir.mkdir(exist_ok=True)
        
    def drop_caches(self):
        """Drop OS page caches for fair benchmarking."""
        try:
            result = subprocess.run(
                ["sudo", "sh", "-c", "echo 3 > /proc/sys/vm/drop_caches"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                print("✅ Dropped OS page caches for fair benchmarking")
                return True
            else:
                print("🟡 Could not drop caches (no sudo), results may show warm cache effects")
                return False
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            print("🟡 Could not drop caches, results may show warm cache effects")
            return False

    def run_synthetic_wkb_tests(self) -> Dict[str, Any]:
        """Run synthetic WKB geometry tests."""
        print(f"\n{'='*80}")
        print("🗺️ RUNNING SYNTHETIC WKB GEOMETRY TESTS")
        print(f"{'='*80}")
        
        self.drop_caches()
        
        try:
            result = subprocess.run(
                ["python", "test_wkb_integration.py"],
                capture_output=True,
                text=True,
                check=True
            )
            
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            
            # Try to find and load the most recent results file
            results_files = sorted(self.test_results_dir.glob("wkb_integration_results_*.json"))
            if results_files:
                with open(results_files[-1]) as f:
                    return json.load(f)
            else:
                return {"status": "completed", "note": "No results file found"}
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Synthetic WKB tests failed: {e}")
            print("STDOUT:", e.stdout)
            print("STDERR:", e.stderr)
            return {"error": str(e), "stdout": e.stdout, "stderr": e.stderr}

    def run_uber_wkb_test(self, mode: str, max_rows: int = 100000, data_file: str = None) -> Dict[str, Any]:
        """Run Uber data WKB integration test for a specific mode."""
        print(f"\n{'='*80}")
        print(f"🚗 RUNNING UBER WKB TEST - {mode.upper()} MODE")
        print(f"{'='*80}")
        
        self.drop_caches()
        
        cmd = ["python", "test_uber_wkb_integration.py", "--mode", mode]
        
        if max_rows:
            cmd.extend(["--max-rows", str(max_rows)])
        
        if data_file:
            cmd.extend(["--data-file", data_file])
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            
            # Try to find and load the most recent results file for this mode
            results_files = sorted(self.test_results_dir.glob(f"uber_wkb_results_{mode}_*.json"))
            if results_files:
                with open(results_files[-1]) as f:
                    return json.load(f)
            else:
                return {"status": "completed", "note": "No results file found"}
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Uber WKB test ({mode}) failed: {e}")
            print("STDOUT:", e.stdout)
            print("STDERR:", e.stderr)
            return {"error": str(e), "stdout": e.stdout, "stderr": e.stderr}

    def run_original_uber_ab_test(self, mode: str, max_rows: int = 100000, data_file: str = None) -> Dict[str, Any]:
        """Run original Uber A/B test for comparison."""
        print(f"\n{'='*80}")
        print(f"📊 RUNNING ORIGINAL UBER A/B TEST - {mode.upper()} MODE")
        print(f"{'='*80}")
        
        self.drop_caches()
        
        cmd = ["python", "test_uber_ab_comparison.py", "--mode", mode]
        
        if max_rows:
            cmd.extend(["--max-rows", str(max_rows)])
        
        if data_file:
            cmd.extend(["--data-file", data_file])
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            
            # Try to find and load the most recent results file for this mode
            results_files = sorted(self.test_results_dir.glob(f"uber_ab_results_{mode}_*.json"))
            if results_files:
                with open(results_files[-1]) as f:
                    return json.load(f)
            else:
                return {"status": "completed", "note": "No results file found"}
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Original Uber A/B test ({mode}) failed: {e}")
            print("STDOUT:", e.stdout)
            print("STDERR:", e.stderr)
            return {"error": str(e), "stdout": e.stdout, "stderr": e.stderr}

    def compare_wkb_results(self, uber_wkb_results: Dict[str, Any], original_results: Dict[str, Any]) -> Dict[str, Any]:
        """Compare WKB-enhanced results with original results."""
        comparison = {
            'comparison_type': 'WKB vs Original',
            'timestamp': time.time(),
        }
        
        # Compare write performance
        if (uber_wkb_results.get('write_performance', {}).get('write_success') and
            original_results.get('write_performance', {}).get('time_seconds')):
            
            wkb_write_time = uber_wkb_results['write_performance']['time_seconds']
            orig_write_time = original_results['write_performance']['time_seconds']
            write_overhead = ((wkb_write_time - orig_write_time) / orig_write_time) * 100
            
            comparison['write_performance'] = {
                'wkb_time': wkb_write_time,
                'original_time': orig_write_time,
                'overhead_percent': write_overhead,
                'analysis': 'positive' if write_overhead < 5 else 'negative'
            }
        
        # Compare dataset sizes
        if (uber_wkb_results.get('write_performance', {}).get('dataset_size_mb') and
            original_results.get('write_performance', {}).get('dataset_size_mb')):
            
            wkb_size = uber_wkb_results['write_performance']['dataset_size_mb']
            orig_size = original_results['write_performance']['dataset_size_mb']
            size_overhead = ((wkb_size - orig_size) / orig_size) * 100
            
            comparison['dataset_size'] = {
                'wkb_size_mb': wkb_size,
                'original_size_mb': orig_size,
                'overhead_percent': size_overhead,
                'analysis': 'acceptable' if size_overhead < 10 else 'concerning'
            }
        
        # Compare spatial query performance
        wkb_queries = uber_wkb_results.get('spatial_query_performance', [])
        orig_queries = original_results.get('spatial_query_performance', [])
        
        query_comparisons = []
        for wkb_query in wkb_queries:
            if 'error' in wkb_query:
                continue
                
            # Find matching query in original results
            matching_orig = None
            for orig_query in orig_queries:
                if ('error' not in orig_query and 
                    wkb_query['name'].replace(' WKB', '') == orig_query['name']):
                    matching_orig = orig_query
                    break
            
            if matching_orig:
                wkb_time = wkb_query['time_seconds']
                orig_time = matching_orig['time_seconds']
                performance_change = ((orig_time - wkb_time) / orig_time) * 100
                
                query_comparisons.append({
                    'query_name': wkb_query['name'],
                    'wkb_time': wkb_time,
                    'original_time': orig_time,
                    'performance_change_percent': performance_change,
                    'wkb_selectivity': wkb_query['selectivity'],
                    'wkb_geometries_returned': wkb_query.get('wkb_geometries_returned', 0),
                })
        
        comparison['spatial_queries'] = query_comparisons
        
        return comparison

    def run_comprehensive_tests(self, modes: List[str] = None, max_rows: int = 100000, 
                               data_file: str = None) -> Dict[str, Any]:
        """Run comprehensive WKB integration tests."""
        if modes is None:
            modes = ["opensource", "custom", "presorted"]
        
        print("🚀 STARTING COMPREHENSIVE WKB INTEGRATION TESTS")
        print(f"📊 Test modes: {modes}")
        print(f"📁 Max rows: {max_rows:,}")
        print(f"📂 Data file: {data_file or 'default'}")
        
        all_results = {
            'test_suite': 'Comprehensive WKB Integration Tests',
            'timestamp': time.time(),
            'modes_tested': modes,
            'max_rows': max_rows,
            'data_file': data_file,
            'results': {}
        }
        
        # Step 1: Run synthetic WKB geometry tests
        print("\n🔬 PHASE 1: SYNTHETIC WKB GEOMETRY TESTS")
        synthetic_results = self.run_synthetic_wkb_tests()
        all_results['results']['synthetic_wkb'] = synthetic_results
        
        # Step 2: Run Uber WKB tests for each mode
        print("\n🚗 PHASE 2: UBER DATA WKB INTEGRATION TESTS")
        uber_wkb_results = {}
        original_uber_results = {}
        
        for mode in modes:
            print(f"\n📊 Testing mode: {mode}")
            
            # Run WKB-enhanced test
            uber_wkb_results[mode] = self.run_uber_wkb_test(mode, max_rows, data_file)
            
            # Run original test for comparison
            original_uber_results[mode] = self.run_original_uber_ab_test(mode, max_rows, data_file)
        
        all_results['results']['uber_wkb'] = uber_wkb_results
        all_results['results']['original_uber'] = original_uber_results
        
        # Step 3: Compare results
        print("\n📊 PHASE 3: COMPARATIVE ANALYSIS")
        comparisons = {}
        
        for mode in modes:
            if mode in uber_wkb_results and mode in original_uber_results:
                comparisons[mode] = self.compare_wkb_results(
                    uber_wkb_results[mode], 
                    original_uber_results[mode]
                )
        
        all_results['results']['comparisons'] = comparisons
        
        return all_results

    def save_comprehensive_results(self, results: Dict[str, Any]) -> Path:
        """Save comprehensive test results."""
        timestamp = int(time.time())
        filename = f"comprehensive_wkb_results_{timestamp}.json"
        filepath = self.test_results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"💾 Comprehensive results saved to: {filepath}")
        return filepath

    def print_comprehensive_summary(self, results: Dict[str, Any]):
        """Print comprehensive test summary."""
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE WKB INTEGRATION TEST SUMMARY")
        print("=" * 80)

        # Synthetic WKB tests summary
        synthetic = results['results'].get('synthetic_wkb', {})
        if 'tests' in synthetic:
            print(f"\n🔬 SYNTHETIC WKB GEOMETRY TESTS:")
            for test_name, test_result in synthetic['tests'].items():
                if test_result.get('write_performance', {}).get('write_success'):
                    print(f"  ✅ {test_name}: Write successful")
                else:
                    print(f"  ❌ {test_name}: Write failed")

        # Uber WKB tests summary
        uber_wkb = results['results'].get('uber_wkb', {})
        original_uber = results['results'].get('original_uber', {})

        print(f"\n🚗 UBER DATA WKB INTEGRATION TESTS:")
        for mode in results['modes_tested']:
            if mode in uber_wkb:
                wkb_result = uber_wkb[mode]
                if wkb_result.get('write_performance', {}).get('write_success'):
                    write_time = wkb_result['write_performance']['time_seconds']
                    dataset_size = wkb_result['write_performance']['dataset_size_mb']
                    print(f"  ✅ {mode.upper()} WKB: {write_time:.3f}s, {dataset_size:.2f}MB")

                    if wkb_result.get('wkb_column_performance', {}).get('wkb_success'):
                        wkb_perf = wkb_result['wkb_column_performance']
                        pickup_count = wkb_perf['pickup_wkb_count']
                        dropoff_count = wkb_perf['dropoff_wkb_count']
                        print(f"    🗺️ WKB geometries: {pickup_count:,} pickup, {dropoff_count:,} dropoff")
                else:
                    print(f"  ❌ {mode.upper()} WKB: Failed")

        # Comparison summary
        comparisons = results['results'].get('comparisons', {})
        if comparisons:
            print(f"\n📊 WKB vs ORIGINAL PERFORMANCE COMPARISON:")

            for mode, comparison in comparisons.items():
                print(f"\n  📈 {mode.upper()} MODE:")

                # Write performance comparison
                if 'write_performance' in comparison:
                    write_comp = comparison['write_performance']
                    overhead = write_comp['overhead_percent']
                    status = "✅" if overhead < 5 else "⚠️" if overhead < 10 else "❌"
                    print(f"    {status} Write overhead: {overhead:+.1f}%")

                # Dataset size comparison
                if 'dataset_size' in comparison:
                    size_comp = comparison['dataset_size']
                    size_overhead = size_comp['overhead_percent']
                    status = "✅" if size_overhead < 5 else "⚠️" if size_overhead < 10 else "❌"
                    print(f"    {status} Size overhead: {size_overhead:+.1f}%")

                # Spatial query performance
                spatial_queries = comparison.get('spatial_queries', [])
                if spatial_queries:
                    print(f"    🗺️ Spatial query performance:")
                    for query in spatial_queries:
                        perf_change = query['performance_change_percent']
                        status = "🚀" if perf_change > 10 else "✅" if perf_change > 0 else "⚠️" if perf_change > -10 else "❌"
                        print(f"      {status} {query['query_name']}: {perf_change:+.1f}% ({query['wkb_geometries_returned']:,} WKB)")

        print(f"\n🎉 Comprehensive WKB integration testing complete!")

def main():
    import argparse

    parser = argparse.ArgumentParser(description="Comprehensive WKB integration testing")
    parser.add_argument("--modes", nargs='+', choices=["opensource", "custom", "presorted"],
                       default=["opensource", "custom"],
                       help="Testing modes to run")
    parser.add_argument("--max-rows", type=int, default=100000,
                       help="Maximum rows to test")
    parser.add_argument("--data-file", type=str,
                       default="uber_data/nyc-taxi/2009/01/data.parquet",
                       help="Path to Uber data file")
    parser.add_argument("--synthetic-only", action="store_true",
                       help="Run only synthetic WKB tests")
    parser.add_argument("--uber-only", action="store_true",
                       help="Run only Uber WKB tests")

    args = parser.parse_args()

    print("=" * 80)
    print("🗺️ COMPREHENSIVE WKB INTEGRATION TESTING SUITE")
    print("=" * 80)
    print(f"📊 Modes: {args.modes}")
    print(f"📁 Max rows: {args.max_rows:,}")
    print(f"📂 Data file: {args.data_file}")

    runner = ComprehensiveWKBTestRunner()

    if args.synthetic_only:
        print("\n🔬 Running synthetic WKB tests only...")
        synthetic_results = runner.run_synthetic_wkb_tests()
        print("🎉 Synthetic WKB tests complete!")
        return

    if args.uber_only:
        print("\n🚗 Running Uber WKB tests only...")
        for mode in args.modes:
            uber_result = runner.run_uber_wkb_test(mode, args.max_rows, args.data_file)
            print(f"✅ {mode} mode complete")
        print("🎉 Uber WKB tests complete!")
        return

    # Run comprehensive tests
    results = runner.run_comprehensive_tests(
        modes=args.modes,
        max_rows=args.max_rows,
        data_file=args.data_file
    )

    # Save results
    results_file = runner.save_comprehensive_results(results)

    # Print summary
    runner.print_comprehensive_summary(results)

    print(f"\n💾 Detailed results saved to: {results_file}")

    return results

if __name__ == "__main__":
    results = main()
