#!/usr/bin/env python3

"""
Debug script to test GeoPage encoding step by step
"""

import lance
import pyarrow as pa
import pandas as pd
import numpy as np
import tempfile
import os

def test_basic_write():
    """Test basic writing with GeoPage encoding"""
    print("🔧 Testing basic GeoPage write...")
    
    # Create simple test data
    data = {
        'pickup_longitude': [-74.0, -73.9, -74.1, -73.8],
        'pickup_latitude': [40.7, 40.8, 40.6, 40.9],
        'trip_id': [1, 2, 3, 4]
    }
    
    df = pd.DataFrame(data)
    table = pa.Table.from_pandas(df)
    
    print(f"📊 Created test data: {len(df)} rows")
    print(f"📊 Columns: {list(df.columns)}")
    
    # Try to write with GeoPage encoding
    with tempfile.TemporaryDirectory() as temp_dir:
        dataset_path = os.path.join(temp_dir, "test_geopage.lance")
        
        try:
            print(f"🔧 Writing to {dataset_path} with GeoPage encoding")

            # Write dataset with GeoPage encoding
            dataset = lance.write_dataset(
                table,
                dataset_path,
                encoding="geopage"
            )
            
            print("✅ Write completed successfully!")
            
            # Try to read back
            print("📖 Attempting to read back...")
            try:
                result = dataset.to_table()
                print(f"✅ Read successful! Got {len(result)} rows")
                return True
                
            except Exception as read_error:
                print(f"❌ Read failed: {read_error}")
                return False
                
        except Exception as write_error:
            print(f"❌ Write failed: {write_error}")
            return False

def test_without_geopage():
    """Test writing without GeoPage encoding as control"""
    print("\n🔧 Testing write WITHOUT GeoPage encoding (control)...")
    
    # Create simple test data
    data = {
        'pickup_longitude': [-74.0, -73.9, -74.1, -73.8],
        'pickup_latitude': [40.7, 40.8, 40.6, 40.9],
        'trip_id': [1, 2, 3, 4]
    }
    
    df = pd.DataFrame(data)
    table = pa.Table.from_pandas(df)
    
    # Try to write without GeoPage encoding
    with tempfile.TemporaryDirectory() as temp_dir:
        dataset_path = os.path.join(temp_dir, "test_normal.lance")
        
        try:
            print(f"🔧 Writing to {dataset_path} with default encoding")
            
            # Write dataset with default encoding
            dataset = lance.write_dataset(table, dataset_path)
            
            print("✅ Write completed successfully!")
            
            # Try to read back
            print("📖 Attempting to read back...")
            result = dataset.to_table()
            print(f"✅ Read successful! Got {len(result)} rows")
            return True
                
        except Exception as error:
            print(f"❌ Control test failed: {error}")
            return False

if __name__ == "__main__":
    print("🚗 GEOPAGE DEBUG TESTING")
    print("=" * 50)
    
    # Test control case first
    control_success = test_without_geopage()
    
    # Test GeoPage case
    geopage_success = test_basic_write()
    
    print("\n📊 RESULTS:")
    print(f"  Control (no GeoPage): {'✅ PASS' if control_success else '❌ FAIL'}")
    print(f"  GeoPage encoding:     {'✅ PASS' if geopage_success else '❌ FAIL'}")
    
    if control_success and not geopage_success:
        print("\n🔍 DIAGNOSIS: GeoPage encoding is causing the issue")
    elif not control_success:
        print("\n🔍 DIAGNOSIS: Basic Lance functionality is broken")
    else:
        print("\n🎉 DIAGNOSIS: Both tests passed!")
