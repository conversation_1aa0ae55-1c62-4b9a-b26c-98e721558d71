{"total_files": 5, "total_size_mb": 2225.23761844635, "total_rows": 70951002, "avg_file_size_mb": 445.04752368927, "avg_rows_per_file": 14190200.4, "file_analyses": [{"file_path": "uber_data/nyc-taxi/2009/01/data.parquet", "file_size_mb": 440.56561183929443, "num_rows": 14092413, "num_columns": 18, "columns": ["vendor_id", "pickup_at", "dropoff_at", "passenger_count", "trip_distance", "pickup_longitude", "pickup_latitude", "rate_code_id", "store_and_fwd_flag", "dropoff_longitude", "dropoff_latitude", "payment_type", "fare_amount", "extra", "mta_tax", "tip_amount", "tolls_amount", "total_amount"], "dtypes": {"vendor_id": "object", "pickup_at": "datetime64[us]", "dropoff_at": "datetime64[us]", "passenger_count": "int8", "trip_distance": "float32", "pickup_longitude": "float32", "pickup_latitude": "float32", "rate_code_id": "object", "store_and_fwd_flag": "object", "dropoff_longitude": "float32", "dropoff_latitude": "float32", "payment_type": "object", "fare_amount": "float32", "extra": "float32", "mta_tax": "float32", "tip_amount": "float32", "tolls_amount": "float32", "total_amount": "float32"}, "spatial_columns": {"longitude": ["pickup_longitude", "dropoff_longitude"], "latitude": ["pickup_latitude", "dropoff_latitude"], "coordinate_pairs": [["pickup_longitude", "pickup_latitude"], ["dropoff_longitude", "dropoff_latitude"]]}, "spatial_analysis": {"pickup_longitude_pickup_latitude": {"valid_rows": 13879079, "invalid_rows": 213334, "validity_rate": 0.9848617834291402, "bbox": {"min_lon": -94.10173034667969, "max_lon": 0.09812700003385544, "min_lat": -7.33510684967041, "max_lat": 81.53500366210938}, "longitude_stats": {"mean": -73.9727783203125, "std": 0.3874507248401642, "median": -73.98168182373047, "q25": -73.99159240722656, "q75": -73.9683609008789}, "latitude_stats": {"mean": 40.75238037109375, "std": 0.218144953250885, "median": 40.754512786865234, "q25": 40.737342834472656, "q75": 40.768348693847656}, "spatial_clustering": {"grid_density_stats": {"mean_density": 38446.202216066486, "max_density": 13874970.0, "std_density": 729248.8469967956, "empty_cells": 305, "total_cells": 361, "sparsity": 0.8448753462603878}, "coordinate_range": {"lon_range": 94.19985961914062, "lat_range": 88.87010955810547}}}, "dropoff_longitude_dropoff_latitude": {"valid_rows": 13882741, "invalid_rows": 209672, "validity_rate": 0.9851216395659139, "bbox": {"min_lon": -94.32691192626953, "max_lon": 0.10140500217676163, "min_lat": -7.33510684967041, "max_lat": 81.53500366210938}, "longitude_stats": {"mean": -73.97163391113281, "std": 0.3911159932613373, "median": -73.98014068603516, "q25": -73.99112701416016, "q75": -73.96531677246094}, "latitude_stats": {"mean": 40.75251007080078, "std": 0.21930384635925293, "median": 40.75477981567383, "q25": 40.73671340942383, "q75": 40.76953887939453}, "spatial_clustering": {"grid_density_stats": {"mean_density": 38456.346260387814, "max_density": 13879573.0, "std_density": 729490.9060656184, "empty_cells": 306, "total_cells": 361, "sparsity": 0.8476454293628809}, "coordinate_range": {"lon_range": 94.42831420898438, "lat_range": 88.87010955810547}}}}, "memory_usage_mb": 2882.082389831543, "sample_rows": [{"vendor_id": "VTS", "pickup_at": "2009-01-04 02:52:00", "dropoff_at": "2009-01-04 03:02:00", "passenger_count": 1, "trip_distance": 2.630000114440918, "pickup_longitude": -73.99195861816406, "pickup_latitude": 40.72156524658203, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": -73.99380493164062, "dropoff_latitude": 40.6959228515625, "payment_type": "CASH", "fare_amount": 8.899999618530273, "extra": 0.5, "mta_tax": NaN, "tip_amount": 0.0, "tolls_amount": 0.0, "total_amount": 9.399999618530273}, {"vendor_id": "VTS", "pickup_at": "2009-01-04 03:31:00", "dropoff_at": "2009-01-04 03:38:00", "passenger_count": 3, "trip_distance": 4.550000190734863, "pickup_longitude": -73.98210144042969, "pickup_latitude": 40.736289978027344, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": -73.95584869384766, "dropoff_latitude": 40.768028259277344, "payment_type": "Credit", "fare_amount": 12.100000381469727, "extra": 0.5, "mta_tax": NaN, "tip_amount": 2.0, "tolls_amount": 0.0, "total_amount": 14.600000381469727}, {"vendor_id": "VTS", "pickup_at": "2009-01-03 15:43:00", "dropoff_at": "2009-01-03 15:57:00", "passenger_count": 5, "trip_distance": 10.350000381469727, "pickup_longitude": -74.0025863647461, "pickup_latitude": 40.73974609375, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": -73.86997985839844, "dropoff_latitude": 40.770225524902344, "payment_type": "Credit", "fare_amount": 23.700000762939453, "extra": 0.0, "mta_tax": NaN, "tip_amount": 4.739999771118164, "tolls_amount": 0.0, "total_amount": 28.***************}]}, {"file_path": "uber_data/nyc-taxi/2009/02/data.parquet", "file_size_mb": 416.1888780593872, "num_rows": 13380122, "num_columns": 18, "columns": ["vendor_id", "pickup_at", "dropoff_at", "passenger_count", "trip_distance", "pickup_longitude", "pickup_latitude", "rate_code_id", "store_and_fwd_flag", "dropoff_longitude", "dropoff_latitude", "payment_type", "fare_amount", "extra", "mta_tax", "tip_amount", "tolls_amount", "total_amount"], "dtypes": {"vendor_id": "object", "pickup_at": "datetime64[us]", "dropoff_at": "datetime64[us]", "passenger_count": "int8", "trip_distance": "float32", "pickup_longitude": "float32", "pickup_latitude": "float32", "rate_code_id": "object", "store_and_fwd_flag": "object", "dropoff_longitude": "float32", "dropoff_latitude": "float32", "payment_type": "object", "fare_amount": "float32", "extra": "float32", "mta_tax": "float32", "tip_amount": "float32", "tolls_amount": "float32", "total_amount": "float32"}, "spatial_columns": {"longitude": ["pickup_longitude", "dropoff_longitude"], "latitude": ["pickup_latitude", "dropoff_latitude"], "coordinate_pairs": [["pickup_longitude", "pickup_latitude"], ["dropoff_longitude", "dropoff_latitude"]]}, "spatial_analysis": {"pickup_longitude_pickup_latitude": {"valid_rows": 13182864, "invalid_rows": 197258, "validity_rate": 0.9852573840507582, "bbox": {"min_lon": -143.25062561035156, "max_lon": 1.2166659832000732, "min_lat": -0.052531998604536057, "max_lat": 67.44808959960938}, "longitude_stats": {"mean": -73.97340393066406, "std": 0.40507692098617554, "median": -73.98192596435547, "q25": -73.99185943603516, "q75": -73.96876525878906}, "latitude_stats": {"mean": 40.751522064208984, "std": 0.22101528942584991, "median": 40.754127502441406, "q25": 40.73726272583008, "q75": 40.76813888549805}, "spatial_clustering": {"grid_density_stats": {"mean_density": 36517.62880886427, "max_density": 13181599.0, "std_density": 692806.6203472759, "empty_cells": 326, "total_cells": 361, "sparsity": 0.9030470914127424}, "coordinate_range": {"lon_range": 144.46728515625, "lat_range": 67.50061798095703}}}, "dropoff_longitude_dropoff_latitude": {"valid_rows": 13186825, "invalid_rows": 193297, "validity_rate": 0.985553420215451, "bbox": {"min_lon": -87.0514907836914, "max_lon": 0.16842299699783325, "min_lat": -0.1303749978542328, "max_lat": 64.48652648925781}, "longitude_stats": {"mean": -73.97172546386719, "std": 0.42982015013694763, "median": -73.98040771484375, "q25": -73.99134826660156, "q75": -73.96562194824219}, "latitude_stats": {"mean": 40.75141525268555, "std": 0.23494559526443481, "median": 40.75438690185547, "q25": 40.73651885986328, "q75": 40.76921463012695}, "spatial_clustering": {"grid_density_stats": {"mean_density": 36528.60110803324, "max_density": 12879247.0, "std_density": 677047.3676292255, "empty_cells": 313, "total_cells": 361, "sparsity": 0.8670360110803325}, "coordinate_range": {"lon_range": 87.21991729736328, "lat_range": 64.61690521240234}}}}, "memory_usage_mb": 2845.264268875122, "sample_rows": [{"vendor_id": "DDS", "pickup_at": "2009-02-03 08:25:00", "dropoff_at": "2009-02-03 08:33:39", "passenger_count": 1, "trip_distance": 1.600000023841858, "pickup_longitude": -73.99276733398438, "pickup_latitude": 40.758323669433594, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": -73.99471282958984, "dropoff_latitude": 40.739723205566406, "payment_type": "CASH", "fare_amount": 6.900000095367432, "extra": 0.0, "mta_tax": NaN, "tip_amount": 0.0, "tolls_amount": 0.0, "total_amount": 6.900000095367432}, {"vendor_id": "VTS", "pickup_at": "2009-02-28 00:26:00", "dropoff_at": "2009-02-28 00:40:00", "passenger_count": 5, "trip_distance": 3.049999952316284, "pickup_longitude": 0.0, "pickup_latitude": 0.0, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": 0.0, "dropoff_latitude": 0.0, "payment_type": "CASH", "fare_amount": 10.5, "extra": 0.5, "mta_tax": NaN, "tip_amount": 0.0, "tolls_amount": 0.0, "total_amount": 11.0}, {"vendor_id": "DDS", "pickup_at": "2009-02-22 00:39:23", "dropoff_at": "2009-02-22 00:45:52", "passenger_count": 1, "trip_distance": 1.5, "pickup_longitude": -73.13739013671875, "pickup_latitude": 41.36613845825195, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": -73.13739013671875, "dropoff_latitude": 41.36613845825195, "payment_type": "CASH", "fare_amount": 5.699999809265137, "extra": 0.5, "mta_tax": NaN, "tip_amount": 0.0, "tolls_amount": 0.0, "total_amount": 6.199999809265137}]}, {"file_path": "uber_data/nyc-taxi/2009/03/data.parquet", "file_size_mb": 452.8005132675171, "num_rows": 14387371, "num_columns": 18, "columns": ["vendor_id", "pickup_at", "dropoff_at", "passenger_count", "trip_distance", "pickup_longitude", "pickup_latitude", "rate_code_id", "store_and_fwd_flag", "dropoff_longitude", "dropoff_latitude", "payment_type", "fare_amount", "extra", "mta_tax", "tip_amount", "tolls_amount", "total_amount"], "dtypes": {"vendor_id": "object", "pickup_at": "datetime64[us]", "dropoff_at": "datetime64[us]", "passenger_count": "int8", "trip_distance": "float32", "pickup_longitude": "float32", "pickup_latitude": "float32", "rate_code_id": "object", "store_and_fwd_flag": "object", "dropoff_longitude": "float32", "dropoff_latitude": "float32", "payment_type": "object", "fare_amount": "float32", "extra": "float32", "mta_tax": "float32", "tip_amount": "float32", "tolls_amount": "float32", "total_amount": "float32"}, "spatial_columns": {"longitude": ["pickup_longitude", "dropoff_longitude"], "latitude": ["pickup_latitude", "dropoff_latitude"], "coordinate_pairs": [["pickup_longitude", "pickup_latitude"], ["dropoff_longitude", "dropoff_latitude"]]}, "spatial_analysis": {"pickup_longitude_pickup_latitude": {"valid_rows": 14192855, "invalid_rows": 194516, "validity_rate": 0.986480087293224, "bbox": {"min_lon": -96.64418029785156, "max_lon": 158.5798797607422, "min_lat": -15.690877914428711, "max_lat": 57.790950775146484}, "longitude_stats": {"mean": -73.96925354003906, "std": 0.6541030406951904, "median": -73.98182678222656, "q25": -73.99172973632812, "q75": -73.96866607666016}, "latitude_stats": {"mean": 40.749393463134766, "std": 0.35512396693229675, "median": 40.75410461425781, "q25": 40.73741912841797, "q75": 40.76789093017578}, "spatial_clustering": {"grid_density_stats": {"mean_density": 39315.38781163435, "max_density": 14191520.0, "std_density": 745886.6768002429, "empty_cells": 324, "total_cells": 361, "sparsity": 0.8975069252077562}, "coordinate_range": {"lon_range": 255.22406005859375, "lat_range": 73.48182678222656}}}, "dropoff_longitude_dropoff_latitude": {"valid_rows": 14196403, "invalid_rows": 190968, "validity_rate": 0.9867266924582677, "bbox": {"min_lon": -116.97599792480469, "max_lon": 158.5798797607422, "min_lat": -53.623531341552734, "max_lat": 55.676025390625}, "longitude_stats": {"mean": -73.96788024902344, "std": 0.6632847189903259, "median": -73.98027801513672, "q25": -73.9912338256836, "q75": -73.96540069580078}, "latitude_stats": {"mean": 40.74942398071289, "std": 0.35994306206703186, "median": 40.75438690185547, "q25": 40.736629486083984, "q75": 40.76905822753906}, "spatial_clustering": {"grid_density_stats": {"mean_density": 39325.216066482, "max_density": 14181504.0, "std_density": 745358.613015237, "empty_cells": 329, "total_cells": 361, "sparsity": 0.9113573407202216}, "coordinate_range": {"lon_range": 275.5558776855469, "lat_range": 109.299560546875}}}}, "memory_usage_mb": 3069.928496360779, "sample_rows": [{"vendor_id": "CMT", "pickup_at": "2009-03-26 15:30:14", "dropoff_at": "2009-03-26 15:33:45", "passenger_count": 1, "trip_distance": 0.30000001192092896, "pickup_longitude": -73.97071075439453, "pickup_latitude": 40.796382904052734, "rate_code_id": null, "store_and_fwd_flag": "0", "dropoff_longitude": -73.97360229492188, "dropoff_latitude": 40.792057037353516, "payment_type": "Cash", "fare_amount": 4.099999904632568, "extra": 0.0, "mta_tax": NaN, "tip_amount": 0.0, "tolls_amount": 0.0, "total_amount": 4.099999904632568}, {"vendor_id": "CMT", "pickup_at": "2009-03-07 00:09:04", "dropoff_at": "2009-03-07 00:16:06", "passenger_count": 1, "trip_distance": 1.600000023841858, "pickup_longitude": -74.00731658935547, "pickup_latitude": 40.73996353149414, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": -74.00471496582031, "dropoff_latitude": 40.75188064575195, "payment_type": "Cash", "fare_amount": 7.0, "extra": 0.0, "mta_tax": NaN, "tip_amount": 0.0, "tolls_amount": 0.0, "total_amount": 7.0}, {"vendor_id": "DDS", "pickup_at": "2009-03-11 19:49:43", "dropoff_at": "2009-03-11 20:00:51", "passenger_count": 1, "trip_distance": 2.0, "pickup_longitude": -73.97637176513672, "pickup_latitude": 40.75672912597656, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": -73.95443725585938, "dropoff_latitude": 40.76720428466797, "payment_type": "CASH", "fare_amount": 8.100000381469727, "extra": 0.5, "mta_tax": NaN, "tip_amount": 0.0, "tolls_amount": 0.0, "total_amount": 8.600000381469727}]}, {"file_path": "uber_data/nyc-taxi/2009/04/data.parquet", "file_size_mb": 449.09880542755127, "num_rows": 14294783, "num_columns": 18, "columns": ["vendor_id", "pickup_at", "dropoff_at", "passenger_count", "trip_distance", "pickup_longitude", "pickup_latitude", "rate_code_id", "store_and_fwd_flag", "dropoff_longitude", "dropoff_latitude", "payment_type", "fare_amount", "extra", "mta_tax", "tip_amount", "tolls_amount", "total_amount"], "dtypes": {"vendor_id": "object", "pickup_at": "datetime64[us]", "dropoff_at": "datetime64[us]", "passenger_count": "int8", "trip_distance": "float32", "pickup_longitude": "float32", "pickup_latitude": "float32", "rate_code_id": "object", "store_and_fwd_flag": "object", "dropoff_longitude": "float32", "dropoff_latitude": "float32", "payment_type": "object", "fare_amount": "float32", "extra": "float32", "mta_tax": "float32", "tip_amount": "float32", "tolls_amount": "float32", "total_amount": "float32"}, "spatial_columns": {"longitude": ["pickup_longitude", "dropoff_longitude"], "latitude": ["pickup_latitude", "dropoff_latitude"], "coordinate_pairs": [["pickup_longitude", "pickup_latitude"], ["dropoff_longitude", "dropoff_latitude"]]}, "spatial_analysis": {"pickup_longitude_pickup_latitude": {"valid_rows": 14088593, "invalid_rows": 206190, "validity_rate": 0.9855758565904778, "bbox": {"min_lon": -101.75151824951172, "max_lon": 8.88785171508789, "min_lat": -0.9673699736595154, "max_lat": 65.6662826538086}, "longitude_stats": {"mean": -73.97319793701172, "std": 0.3539330065250397, "median": -73.98172760009766, "q25": -73.99173736572266, "q75": -73.96835327148438}, "latitude_stats": {"mean": 40.75183868408203, "std": 0.19817250967025757, "median": 40.754486083984375, "q25": 40.737342834472656, "q75": 40.76838684082031}, "spatial_clustering": {"grid_density_stats": {"mean_density": 39026.57340720222, "max_density": 14067373.0, "std_density": 739359.4829241546, "empty_cells": 292, "total_cells": 361, "sparsity": 0.8088642659279779}, "coordinate_range": {"lon_range": 110.63937377929688, "lat_range": 66.63365173339844}}}, "dropoff_longitude_dropoff_latitude": {"valid_rows": 14092134, "invalid_rows": 202649, "validity_rate": 0.9858235693399473, "bbox": {"min_lon": -96.95903778076172, "max_lon": 113.71126556396484, "min_lat": -12.748987197875977, "max_lat": 65.3726806640625}, "longitude_stats": {"mean": -73.97209167480469, "std": 0.36417293548583984, "median": -73.98015594482422, "q25": -73.9912109375, "q75": -73.96542358398438}, "latitude_stats": {"mean": 40.75193405151367, "std": 0.19853083789348602, "median": 40.75476837158203, "q25": 40.736698150634766, "q75": 40.76932144165039}, "spatial_clustering": {"grid_density_stats": {"mean_density": 39036.382271468145, "max_density": 13469598.0, "std_density": 708605.2308021353, "empty_cells": 312, "total_cells": 361, "sparsity": 0.8642659279778393}, "coordinate_range": {"lon_range": 210.67030334472656, "lat_range": 78.12166595458984}}}}, "memory_usage_mb": 3086.0296897888184, "sample_rows": [{"vendor_id": "VTS", "pickup_at": "2009-04-08 12:19:00", "dropoff_at": "2009-04-08 12:24:00", "passenger_count": 1, "trip_distance": 0.49000000953674316, "pickup_longitude": -73.9744644165039, "pickup_latitude": 40.76079177856445, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": -73.96676635742188, "dropoff_latitude": 40.757057189941406, "payment_type": "CASH", "fare_amount": 4.099999904632568, "extra": 0.0, "mta_tax": NaN, "tip_amount": 0.0, "tolls_amount": 0.0, "total_amount": 4.099999904632568}, {"vendor_id": "VTS", "pickup_at": "2009-04-08 15:20:00", "dropoff_at": "2009-04-08 15:28:00", "passenger_count": 1, "trip_distance": 1.0499999523162842, "pickup_longitude": -73.97853088378906, "pickup_latitude": 40.75347137451172, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": -73.98124694824219, "dropoff_latitude": 40.7654914855957, "payment_type": "Credit", "fare_amount": 6.099999904632568, "extra": 0.0, "mta_tax": NaN, "tip_amount": 0.8999999761581421, "tolls_amount": 0.0, "total_amount": 7.0}, {"vendor_id": "VTS", "pickup_at": "2009-04-06 21:31:00", "dropoff_at": "2009-04-06 21:35:00", "passenger_count": 1, "trip_distance": 1.1399999856948853, "pickup_longitude": -73.99579620361328, "pickup_latitude": 40.73893356323242, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": -74.00208282470703, "dropoff_latitude": 40.74767303466797, "payment_type": "Credit", "fare_amount": 5.300000190734863, "extra": 0.5, "mta_tax": NaN, "tip_amount": 1.5, "tolls_amount": 0.0, "total_amount": 7.300000190734863}]}, {"file_path": "uber_data/nyc-taxi/2009/05/data.parquet", "file_size_mb": 466.5838098526001, "num_rows": 14796313, "num_columns": 18, "columns": ["vendor_id", "pickup_at", "dropoff_at", "passenger_count", "trip_distance", "pickup_longitude", "pickup_latitude", "rate_code_id", "store_and_fwd_flag", "dropoff_longitude", "dropoff_latitude", "payment_type", "fare_amount", "extra", "mta_tax", "tip_amount", "tolls_amount", "total_amount"], "dtypes": {"vendor_id": "object", "pickup_at": "datetime64[us]", "dropoff_at": "datetime64[us]", "passenger_count": "int8", "trip_distance": "float32", "pickup_longitude": "float32", "pickup_latitude": "float32", "rate_code_id": "object", "store_and_fwd_flag": "object", "dropoff_longitude": "float32", "dropoff_latitude": "float32", "payment_type": "object", "fare_amount": "float32", "extra": "float32", "mta_tax": "float32", "tip_amount": "float32", "tolls_amount": "float32", "total_amount": "float32"}, "spatial_columns": {"longitude": ["pickup_longitude", "dropoff_longitude"], "latitude": ["pickup_latitude", "dropoff_latitude"], "coordinate_pairs": [["pickup_longitude", "pickup_latitude"], ["dropoff_longitude", "dropoff_latitude"]]}, "spatial_analysis": {"pickup_longitude_pickup_latitude": {"valid_rows": 14575481, "invalid_rows": 220832, "validity_rate": 0.985075200828747, "bbox": {"min_lon": -95.70646667480469, "max_lon": 62.88608932495117, "min_lat": -35.15403747558594, "max_lat": 81.50907135009766}, "longitude_stats": {"mean": -73.97230529785156, "std": 0.43971213698387146, "median": -73.98179626464844, "q25": -73.99190521240234, "q75": -73.96818542480469}, "latitude_stats": {"mean": 40.750850677490234, "std": 0.24692772328853607, "median": 40.75425338745117, "q25": 40.73704528808594, "q75": 40.76850891113281}, "spatial_clustering": {"grid_density_stats": {"mean_density": 40375.29362880887, "max_density": 14574368.0, "std_density": 766008.6747159172, "empty_cells": 320, "total_cells": 361, "sparsity": 0.8864265927977839}, "coordinate_range": {"lon_range": 158.59255981445312, "lat_range": 116.6631088256836}}}, "dropoff_longitude_dropoff_latitude": {"valid_rows": 14579804, "invalid_rows": 216509, "validity_rate": 0.9853673682085531, "bbox": {"min_lon": -101.56109619140625, "max_lon": 62.88608932495117, "min_lat": -35.15403747558594, "max_lat": 81.50907135009766}, "longitude_stats": {"mean": -73.97108459472656, "std": 0.4464435577392578, "median": -73.98023223876953, "q25": -73.9913330078125, "q75": -73.9652328491211}, "latitude_stats": {"mean": 40.75088882446289, "std": 0.2527468502521515, "median": 40.75469207763672, "q25": 40.736392974853516, "q75": 40.76939010620117}, "spatial_clustering": {"grid_density_stats": {"mean_density": 40387.26869806094, "max_density": 14578472.0, "std_density": 766224.3435100155, "empty_cells": 318, "total_cells": 361, "sparsity": 0.8808864265927978}, "coordinate_range": {"lon_range": 164.*************, "lat_range": 116.6631088256836}}}}, "memory_usage_mb": 3192.073088645935, "sample_rows": [{"vendor_id": "CMT", "pickup_at": "2009-05-27 07:41:05", "dropoff_at": "2009-05-27 07:42:28", "passenger_count": 1, "trip_distance": 0.30000001192092896, "pickup_longitude": -73.97410583496094, "pickup_latitude": 40.74289321899414, "rate_code_id": null, "store_and_fwd_flag": "0", "dropoff_longitude": -73.97377014160156, "dropoff_latitude": 40.74640655517578, "payment_type": "Credit", "fare_amount": 3.299999952316284, "extra": 0.0, "mta_tax": NaN, "tip_amount": 1.0, "tolls_amount": 0.0, "total_amount": 4.300000190734863}, {"vendor_id": "CMT", "pickup_at": "2009-05-27 07:51:06", "dropoff_at": "2009-05-27 07:58:43", "passenger_count": 1, "trip_distance": 1.899999976158142, "pickup_longitude": -74.00814819335938, "pickup_latitude": 40.738853454589844, "rate_code_id": null, "store_and_fwd_flag": "0", "dropoff_longitude": -74.01564025878906, "dropoff_latitude": 40.714534759521484, "payment_type": "Cash", "fare_amount": 7.300000190734863, "extra": 0.0, "mta_tax": NaN, "tip_amount": 0.0, "tolls_amount": 0.0, "total_amount": 7.300000190734863}, {"vendor_id": "DDS", "pickup_at": "2009-05-15 15:22:02", "dropoff_at": "2009-05-15 15:30:15", "passenger_count": 3, "trip_distance": 1.399999976158142, "pickup_longitude": -73.97334289550781, "pickup_latitude": 40.76404571533203, "rate_code_id": null, "store_and_fwd_flag": null, "dropoff_longitude": -73.9526596069336, "dropoff_latitude": 40.769893646240234, "payment_type": "CREDIT", "fare_amount": 6.099999904632568, "extra": 0.0, "mta_tax": NaN, "tip_amount": 1.0, "tolls_amount": 0.0, "total_amount": 7.099999904632568}]}], "common_schema": {"all_columns": ["dropoff_at", "dropoff_latitude", "dropoff_longitude", "extra", "fare_amount", "mta_tax", "passenger_count", "payment_type", "pickup_at", "pickup_latitude", "pickup_longitude", "rate_code_id", "store_and_fwd_flag", "tip_amount", "tolls_amount", "total_amount", "trip_distance", "vendor_id"], "spatial_columns": ["dropoff_latitude", "dropoff_longitude", "pickup_latitude", "pickup_longitude"], "total_unique_columns": 18}, "analysis_duration_seconds": 42.05953884124756}