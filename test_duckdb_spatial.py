#!/usr/bin/env python3

"""
DuckDB spatial performance comparison between GeoPage and standard Lance datasets.
Uses DuckDB's spatial extension to run realistic spatial queries.
"""

import duckdb
import time
import json
import lance
import pandas as pd
import os
from pathlib import Path

def setup_duckdb():
    """Setup DuckDB with spatial extension."""
    print("🦆 Setting up DuckDB with spatial extension...")
    
    conn = duckdb.connect()
    
    # Install and load spatial extension
    conn.execute("INSTALL spatial;")
    conn.execute("LOAD spatial;")
    
    # Enable object cache to keep Lance files open
    conn.execute("PRAGMA enable_object_cache;")
    
    print("✅ DuckDB spatial extension loaded")
    return conn

def register_datasets(conn, sample_size=None):
    """Register both WKB Lance datasets directly in DuckDB."""
    print("📊 Registering WKB Lance datasets in DuckDB...")

    # Load WKB Lance datasets - DuckDB can use them directly
    print("  🔄 Loading open source WKB Lance dataset...")
    std_dataset = lance.dataset("datasets/uber_wkb_opensource")

    print("  🔄 Loading GeoPage WKB Lance dataset...")
    geo_dataset = lance.dataset("datasets/uber_wkb_custom")

    # Register datasets in DuckDB using Lance's Arrow integration (memory efficient)
    print("  🔗 Registering datasets in DuckDB...")
    if sample_size:
        print(f"  📊 Using sample size: {sample_size:,} rows for testing")
        # Use to_lance() for memory-efficient access, then limit in DuckDB
        std_lance = std_dataset.to_lance()
        geo_lance = geo_dataset.to_lance()
        conn.register("std_dataset_full", std_lance)
        conn.register("geo_dataset_full", geo_lance)
        # Create limited views
        conn.execute(f"CREATE VIEW std_dataset AS SELECT * FROM std_dataset_full LIMIT {sample_size}")
        conn.execute(f"CREATE VIEW geo_dataset AS SELECT * FROM geo_dataset_full LIMIT {sample_size}")
    else:
        print("  📊 Using full datasets (memory efficient with to_lance())...")
        # Use to_lance() for memory-efficient access without loading into memory
        conn.register("std_dataset", std_dataset.to_lance())
        conn.register("geo_dataset", geo_dataset.to_lance())

    # Verify datasets
    std_count = conn.execute("SELECT COUNT(*) FROM std_dataset").fetchone()[0]
    geo_count = conn.execute("SELECT COUNT(*) FROM geo_dataset").fetchone()[0]

    print(f"✅ Open source WKB dataset: {std_count:,} rows")
    print(f"✅ GeoPage WKB dataset: {geo_count:,} rows")

    return std_count, geo_count, std_dataset, geo_dataset

def drop_caches():
    """Drop OS page caches for fair benchmarking."""
    try:
        import subprocess
        result = subprocess.run(
            ["sudo", "sh", "-c", "echo 3 > /proc/sys/vm/drop_caches"],
            capture_output=True, text=True, timeout=5
        )
        if result.returncode == 0:
            print("✅ Dropped OS page caches")
            return True
        else:
            print("🟡 Could not drop caches (no sudo)")
            return False
    except:
        print("🟡 Could not drop caches")
        return False

def run_spatial_queries(conn, std_dataset, geo_dataset):
    """Run spatial queries on both datasets and compare performance."""
    print("\n🗺️ Running spatial performance comparison...")

    # Define spatial queries - both coordinate filters AND WKB spatial functions
    spatial_queries = [
        {
            'name': 'Manhattan Core (Coordinates)',
            'description': 'High-density Manhattan area using coordinate filters',
            'filter': """
                pickup_longitude >= -74.01 AND pickup_longitude <= -73.97 AND
                pickup_latitude >= 40.74 AND pickup_latitude <= 40.78
            """
        },
        {
            'name': 'Manhattan Core (WKB)',
            'description': 'High-density Manhattan area using WKB spatial intersection',
            'filter': """
                pickup_point_wkb IS NOT NULL AND
                octet_length(pickup_point_wkb) = 21 AND
                ST_Intersects(
                    ST_GeomFromWKB(pickup_point_wkb),
                    ST_MakeEnvelope(-74.01, 40.74, -73.97, 40.78)
                )
            """
        },
        {
            'name': 'Small Area (Coordinates)',
            'description': 'Small specific area using coordinate filters',
            'filter': """
                pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND
                pickup_latitude >= 40.755 AND pickup_latitude <= 40.765
            """
        },
        {
            'name': 'Small Area (WKB)',
            'description': 'Small specific area using WKB spatial intersection',
            'filter': """
                pickup_point_wkb IS NOT NULL AND
                octet_length(pickup_point_wkb) = 21 AND
                ST_Intersects(
                    ST_GeomFromWKB(pickup_point_wkb),
                    ST_MakeEnvelope(-73.985, 40.755, -73.975, 40.765)
                )
            """
        },
        {
            'name': 'Airport Area (WKB)',
            'description': 'JFK Airport vicinity using WKB spatial intersection',
            'filter': """
                dropoff_point_wkb IS NOT NULL AND
                octet_length(dropoff_point_wkb) = 21 AND
                ST_Intersects(
                    ST_GeomFromWKB(dropoff_point_wkb),
                    ST_MakeEnvelope(-73.82, 40.63, -73.76, 40.67)
                )
            """
        }
    ]
    
    results = []

    for query_info in spatial_queries:
        print(f"\n🎯 Testing: {query_info['name']}")

        # Test standard Lance dataset
        print("  🔄 Testing open source Lance...")
        drop_caches()  # Fair comparison
        std_query = f"SELECT COUNT(*) FROM std_dataset WHERE {query_info['filter']}"
        start_time = time.time()
        std_result = conn.execute(std_query).fetchone()[0]
        std_time = time.time() - start_time

        # Test GeoPage Lance dataset
        print("  🔄 Testing GeoPage Lance...")
        drop_caches()  # Fair comparison
        geo_query = f"SELECT COUNT(*) FROM geo_dataset WHERE {query_info['filter']}"
        start_time = time.time()
        geo_result = conn.execute(geo_query).fetchone()[0]
        geo_time = time.time() - start_time
        
        # Calculate improvement
        improvement = ((std_time - geo_time) / std_time) * 100 if std_time > 0 else 0
        
        result = {
            'name': query_info['name'],
            'description': query_info['description'],
            'std_time': std_time,
            'geo_time': geo_time,
            'std_result': std_result,
            'geo_result': geo_result,
            'improvement_percent': improvement,
            'results_match': std_result == geo_result
        }
        
        results.append(result)
        
        print(f"  📊 Standard Lance: {std_time:.3f}s ({std_result:,} rows)")
        print(f"  🗺️ GeoPage Lance:  {geo_time:.3f}s ({geo_result:,} rows)")
        print(f"  📈 Improvement:    {improvement:+.1f}%")
        print(f"  ✅ Results match:  {result['results_match']}")
    
    return results

def run_full_scan_comparison(conn, std_dataset, geo_dataset):
    """Compare full scan performance."""
    print("\n📖 Running full scan comparison...")

    # Standard Lance full scan
    start_time = time.time()
    std_count = conn.execute("SELECT COUNT(*) FROM std_dataset").fetchone()[0]
    std_time = time.time() - start_time

    # GeoPage Lance full scan
    start_time = time.time()
    geo_count = conn.execute("SELECT COUNT(*) FROM geo_dataset").fetchone()[0]
    geo_time = time.time() - start_time
    
    improvement = ((std_time - geo_time) / std_time) * 100 if std_time > 0 else 0
    
    print(f"  📊 Standard Lance: {std_time:.3f}s ({std_count:,} rows)")
    print(f"  🗺️ GeoPage Lance:  {geo_time:.3f}s ({geo_count:,} rows)")
    print(f"  📈 Improvement:    {improvement:+.1f}%")
    
    return {
        'std_time': std_time,
        'geo_time': geo_time,
        'std_count': std_count,
        'geo_count': geo_count,
        'improvement_percent': improvement,
        'counts_match': std_count == geo_count
    }

def save_results(spatial_results, full_scan_result):
    """Save DuckDB test results."""
    results = {
        'test_type': 'duckdb_spatial_comparison',
        'timestamp': int(time.time()),
        'full_scan': full_scan_result,
        'spatial_queries': spatial_results,
        'summary': {
            'avg_spatial_improvement': sum(r['improvement_percent'] for r in spatial_results) / len(spatial_results),
            'best_improvement': max(r['improvement_percent'] for r in spatial_results),
            'worst_improvement': min(r['improvement_percent'] for r in spatial_results),
            'all_results_match': all(r['results_match'] for r in spatial_results) and full_scan_result['counts_match']
        }
    }
    
    # Save to file
    output_file = Path("test_results") / f"duckdb_spatial_results_{int(time.time())}.json"
    output_file.parent.mkdir(exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: {output_file}")
    return results

def cleanup_temp_files():
    """Clean up temporary Parquet files."""
    temp_files = ["temp_taxi_std.parquet", "temp_taxi_geo.parquet"]
    for file in temp_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ Cleaned up {file}")

def main():
    import argparse

    parser = argparse.ArgumentParser(description="DuckDB Spatial Performance Testing on WKB datasets")
    parser.add_argument("--sample-size", type=int, default=100000,
                       help="Sample size for testing (default: 100,000 rows)")
    parser.add_argument("--full", action='store_true',
                       help="Use full datasets (12M rows) - may be slow")

    args = parser.parse_args()

    print("🦆 DUCKDB SPATIAL PERFORMANCE COMPARISON (WKB DATASETS)")
    print("=" * 70)

    try:
        # Setup DuckDB
        conn = setup_duckdb()

        # Register datasets
        sample_size = None if args.full else args.sample_size
        std_count, geo_count, std_dataset, geo_dataset = register_datasets(conn, sample_size)

        if std_count != geo_count:
            print(f"⚠️ Warning: Dataset sizes don't match ({std_count} vs {geo_count})")

        # Run full scan comparison
        full_scan_result = run_full_scan_comparison(conn, std_dataset, geo_dataset)

        # Run spatial queries
        spatial_results = run_spatial_queries(conn, std_dataset, geo_dataset)
        
        # Save results
        results = save_results(spatial_results, full_scan_result)
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 SUMMARY")
        print("=" * 60)
        print(f"📖 Full scan improvement: {full_scan_result['improvement_percent']:+.1f}%")
        print(f"🗺️ Average spatial improvement: {results['summary']['avg_spatial_improvement']:+.1f}%")
        print(f"🎯 Best spatial improvement: {results['summary']['best_improvement']:+.1f}%")
        print(f"✅ All results match: {results['summary']['all_results_match']}")
        
        if results['summary']['avg_spatial_improvement'] > 0:
            print("🎉 GeoPage shows performance benefits!")
        else:
            print("🤔 GeoPage needs optimization - no performance benefits yet")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    
    finally:
        if 'conn' in locals():
            conn.close()
        cleanup_temp_files()

    return 0

if __name__ == "__main__":
    exit(main())
