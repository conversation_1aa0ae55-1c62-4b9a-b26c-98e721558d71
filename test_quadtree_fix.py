#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Test script to validate the quadtree loading fix
Creates WKB Point geometries and tests if quadtree nodes are properly loaded
"""

import os
import sys
import tempfile
import time
import pandas as pd
import numpy as np
import struct

# Add the venv to Python path
venv_path = "/home/<USER>/Work/terrafloww/geo-research/venv/lib/python3.12/site-packages"
if venv_path not in sys.path:
    sys.path.insert(0, venv_path)

try:
    import lance
    print("✅ Lance imported successfully")
    print(f"📦 Lance version: {lance.__version__}")
except ImportError as e:
    print(f"❌ Failed to import lance: {e}")
    sys.exit(1)

def create_wkb_point(lon, lat):
    """Create a WKB Point geometry from longitude/latitude coordinates"""
    wkb_data = struct.pack('<BIdd', 1, 1, lon, lat)
    return wkb_data

def test_quadtree_loading():
    """Test if quadtree nodes are properly loaded from the bvh field"""
    print("\n🚀 Testing quadtree loading fix...")
    
    np.random.seed(42)
    n_rows = 5000
    
    # NYC area coordinates
    lon_min, lon_max = -74.1, -73.8
    lat_min, lat_max = 40.6, 40.9
    
    # Generate coordinate pairs
    longitudes = np.random.uniform(lon_min, lon_max, n_rows)
    latitudes = np.random.uniform(lat_min, lat_max, n_rows)
    
    # Create WKB Point geometries
    geometries = []
    for lon, lat in zip(longitudes, latitudes):
        wkb_point = create_wkb_point(lon, lat)
        geometries.append(wkb_point)
    
    # Create DataFrame with geometry column
    data = {
        'pickup_point': geometries,  # WKB Point geometry
        'pickup_longitude': longitudes,  # Keep original coordinates for filtering
        'pickup_latitude': latitudes,
        'trip_id': range(n_rows),
        'fare': np.random.uniform(5.0, 50.0, n_rows)
    }
    
    df = pd.DataFrame(data)
    print(f"📊 Created dataset with {len(df)} rows")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        dataset_path = os.path.join(temp_dir, 'test_quadtree_fix')
        
        # Write dataset with GeoPage encoding
        print("\n💾 Writing dataset with GeoPage encoding...")
        start_time = time.time()
        dataset = lance.write_dataset(df, dataset_path, encoding='geopage')
        write_time = time.time() - start_time
        print(f"✅ Dataset written in {write_time:.3f}s")
        
        # Test spatial query - this should trigger quadtree loading
        print("\n🔍 Testing spatial query (should load quadtree nodes)...")
        query_bbox = (-74.0, 40.7, -73.9, 40.8)  # Manhattan core area
        
        filter_query = f'''
            pickup_longitude >= {query_bbox[0]} AND pickup_longitude <= {query_bbox[2]} AND 
            pickup_latitude >= {query_bbox[1]} AND pickup_latitude <= {query_bbox[3]}
        '''
        
        print(f"🗺️ Query filter: {filter_query.strip()}")
        
        start_time = time.time()
        result = dataset.to_table(filter=filter_query)
        query_time = time.time() - start_time
        
        print(f"✅ Query completed in {query_time:.3f}s")
        print(f"📊 Query returned {len(result)} rows")
        
        # Check if results are within bounds
        if len(result) > 0:
            result_df = result.to_pandas()
            lon_range = (result_df['pickup_longitude'].min(), result_df['pickup_longitude'].max())
            lat_range = (result_df['pickup_latitude'].min(), result_df['pickup_latitude'].max())
            
            print(f"📍 Result longitude range: {lon_range[0]:.3f} to {lon_range[1]:.3f}")
            print(f"📍 Result latitude range: {lat_range[0]:.3f} to {lat_range[1]:.3f}")
            
            within_bounds = (
                lon_range[0] >= query_bbox[0] and lon_range[1] <= query_bbox[2] and
                lat_range[0] >= query_bbox[1] and lat_range[1] <= query_bbox[3]
            )
            
            if within_bounds:
                print("✅ Spatial filtering working correctly")
            else:
                print("⚠️ Some results outside query bounds")
        
        # Performance comparison
        print("\n📈 Performance comparison...")
        start_time = time.time()
        full_result = dataset.to_table()
        full_scan_time = time.time() - start_time
        
        selectivity = len(result) / len(full_result) if len(full_result) > 0 else 0
        speedup = full_scan_time / query_time if query_time > 0 else 0
        
        print(f"📊 Full scan: {len(full_result)} rows in {full_scan_time:.3f}s")
        print(f"📊 Spatial query: {len(result)} rows in {query_time:.3f}s")
        print(f"📊 Selectivity: {selectivity:.1%}")
        print(f"📊 Speedup: {speedup:.1f}x")
        
        return {
            'rows_total': len(full_result),
            'rows_filtered': len(result),
            'selectivity': selectivity,
            'full_scan_time': full_scan_time,
            'spatial_query_time': query_time,
            'speedup': speedup
        }

if __name__ == "__main__":
    try:
        results = test_quadtree_loading()
        print(f"\n🎉 Quadtree loading test completed!")
        print(f"📊 Results: {results}")
        
        if results['speedup'] > 1.0:
            print("✅ Spatial optimization is working!")
        else:
            print("⚠️ Limited spatial optimization detected")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
