#!/usr/bin/env python3
"""
Compare WKB A/B test results between open-source and custom Lance
"""

import json
import sys

def load_json_results(filepath):
    """Load JSON results file"""
    try:
        with open(filepath, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ Error: File not found: {filepath}")
        return None
    except json.JSONDecodeError:
        print(f"❌ Error: Invalid JSON in file: {filepath}")
        return None

def compare_wkb_results(opensource_file, custom_file):
    """Compare two WKB JSON result files"""
    print("🎯 WKB A/B TEST RESULTS COMPARISON")
    print("=" * 60)
    
    # Load both files
    opensource = load_json_results(opensource_file)
    custom = load_json_results(custom_file)
    
    if not opensource or not custom:
        return None
    
    print(f"✅ Loaded open-source results: {opensource['data_info']['rows']:,} rows")
    print(f"✅ Loaded custom results: {custom['data_info']['rows']:,} rows")
    
    # Data integrity check
    print(f"\n📊 DATA INTEGRITY COMPARISON")
    print("-" * 40)
    
    os_pickup_wkb = int(opensource['data_info']['pickup_wkb_count'])
    custom_pickup_wkb = int(custom['data_info']['pickup_wkb_count'])
    os_dropoff_wkb = int(opensource['data_info']['dropoff_wkb_count'])
    custom_dropoff_wkb = int(custom['data_info']['dropoff_wkb_count'])
    
    print(f"📍 Pickup WKB Geometries:")
    print(f"   Open Source: {os_pickup_wkb:,}")
    print(f"   Custom:      {custom_pickup_wkb:,}")
    print(f"   Match:       {'✅' if os_pickup_wkb == custom_pickup_wkb else '❌'}")
    
    print(f"\n📍 Dropoff WKB Geometries:")
    print(f"   Open Source: {os_dropoff_wkb:,}")
    print(f"   Custom:      {custom_dropoff_wkb:,}")
    print(f"   Match:       {'✅' if os_dropoff_wkb == custom_dropoff_wkb else '❌'}")
    
    # Write performance comparison
    print(f"\n📝 WRITE PERFORMANCE COMPARISON")
    print("-" * 40)

    os_write = opensource['write_performance']['time_seconds']
    custom_write = custom['write_performance']['time_seconds']

    # Handle existing datasets (write time = 0)
    if os_write > 0 and custom_write > 0:
        write_improvement = ((os_write - custom_write) / os_write) * 100
        print(f"⏱️ Write Time:")
        print(f"   Open Source: {os_write:.3f}s")
        print(f"   Custom:      {custom_write:.3f}s")
        print(f"   Change:      {write_improvement:+.1f}%")
    else:
        write_improvement = 0
        print(f"⏱️ Write Time: N/A (using existing datasets)")
        if opensource['write_performance'].get('existing_dataset'):
            print(f"   Both datasets loaded from existing files")
    
    # Dataset size comparison
    os_size = opensource['write_performance']['dataset_size_mb']
    custom_size = custom['write_performance']['dataset_size_mb']
    size_change = ((custom_size - os_size) / os_size) * 100
    
    print(f"\n💾 Dataset Size:")
    print(f"   Open Source: {os_size:.2f}MB")
    print(f"   Custom:      {custom_size:.2f}MB")
    print(f"   Change:      {size_change:+.1f}%")
    
    # Compression ratio
    os_compression = opensource['write_performance']['compression_ratio']
    custom_compression = custom['write_performance']['compression_ratio']
    
    print(f"\n🗜️ Compression Ratio:")
    print(f"   Open Source: {os_compression:.2f}x")
    print(f"   Custom:      {custom_compression:.2f}x")
    
    # WKB column access performance
    print(f"\n📖 WKB COLUMN ACCESS PERFORMANCE")
    print("-" * 40)

    os_wkb_success = opensource['wkb_column_performance'].get('wkb_success', False)
    custom_wkb_success = custom['wkb_column_performance'].get('wkb_success', False)

    if os_wkb_success and custom_wkb_success:
        os_wkb_access = opensource['wkb_column_performance']['time_seconds']
        custom_wkb_access = custom['wkb_column_performance']['time_seconds']

        if os_wkb_access > 0:
            wkb_improvement = ((os_wkb_access - custom_wkb_access) / os_wkb_access) * 100
            print(f"⏱️ WKB Access Time:")
            print(f"   Open Source: {os_wkb_access:.3f}s")
            print(f"   Custom:      {custom_wkb_access:.3f}s")
            print(f"   Improvement: {wkb_improvement:+.1f}%")
        else:
            wkb_improvement = 0
            print(f"⏱️ WKB Access Time: N/A (both skipped)")
    else:
        print(f"⚠️ WKB Column Access:")
        print(f"   Open Source: {'✅' if os_wkb_success else '❌'}")
        print(f"   Custom:      {'✅' if custom_wkb_success else '❌'}")
        if not custom_wkb_success:
            if custom['wkb_column_performance'].get('skipped'):
                reason = custom['wkb_column_performance'].get('reason', 'Unknown reason')
                print(f"   Custom Skipped: {reason}")
            else:
                error_msg = custom['wkb_column_performance'].get('error', 'Unknown error')
                print(f"   Custom Error: {error_msg[:100]}...")
        wkb_improvement = 0  # Set to 0 if we can't compare
    
    # Spatial query comparison
    print(f"\n🗺️ SPATIAL QUERY PERFORMANCE COMPARISON")
    print("-" * 50)
    
    os_spatial = {q['name']: q for q in opensource['spatial_query_performance']}
    custom_spatial = {q['name']: q for q in custom['spatial_query_performance']}
    
    spatial_improvements = []
    
    for query_name in os_spatial.keys():
        if query_name in custom_spatial:
            os_query = os_spatial[query_name]
            custom_query = custom_spatial[query_name]
            
            os_time = os_query['time_seconds']
            custom_time = custom_query['time_seconds']
            os_rows = os_query['rows_returned']
            custom_rows = custom_query['rows_returned']
            os_selectivity = os_query['selectivity']
            custom_selectivity = custom_query['selectivity']
            
            improvement = ((os_time - custom_time) / os_time) * 100
            spatial_improvements.append(improvement)
            
            print(f"\n🎯 {query_name}:")
            print(f"   Open Source: {os_time:.3f}s ({os_rows:,} rows, {os_selectivity*100:.1f}%)")
            print(f"   Custom:      {custom_time:.3f}s ({custom_rows:,} rows, {custom_selectivity*100:.1f}%)")
            print(f"   Improvement: {improvement:+.1f}%")
            print(f"   Rows Match:  {'✅' if os_rows == custom_rows else '❌'}")
    
    # Summary statistics
    print(f"\n📈 PERFORMANCE SUMMARY")
    print("-" * 30)
    
    if spatial_improvements:
        avg_spatial_improvement = sum(spatial_improvements) / len(spatial_improvements)
        best_improvement = max(spatial_improvements)
        worst_improvement = min(spatial_improvements)
        
        print(f"🚀 Spatial Query Performance:")
        print(f"   Average improvement: {avg_spatial_improvement:+.1f}%")
        print(f"   Best improvement:    {best_improvement:+.1f}%")
        print(f"   Worst improvement:   {worst_improvement:+.1f}%")
    
    print(f"\n💾 Storage Performance:")
    print(f"   Write time change:   {write_improvement:+.1f}%")
    print(f"   Dataset size change: {size_change:+.1f}%")
    print(f"   WKB access change:   {wkb_improvement:+.1f}%")
    
    # Overall assessment
    print(f"\n🎉 OVERALL ASSESSMENT")
    print("=" * 30)
    
    # Data integrity check
    data_integrity = (os_pickup_wkb == custom_pickup_wkb and 
                     os_dropoff_wkb == custom_dropoff_wkb)
    
    print(f"✅ Data Integrity: {'PERFECT' if data_integrity else 'ISSUES DETECTED'}")
    
    if spatial_improvements:
        if avg_spatial_improvement > 0:
            print(f"✅ Spatial Performance: IMPROVED ({avg_spatial_improvement:+.1f}% average)")
        elif avg_spatial_improvement > -5:
            print(f"🟡 Spatial Performance: NEUTRAL ({avg_spatial_improvement:+.1f}% average)")
        else:
            print(f"❌ Spatial Performance: DEGRADED ({avg_spatial_improvement:+.1f}% average)")
    
    if size_change > 0:
        print(f"📊 Storage Overhead: {size_change:.1f}% larger (spatial indexing cost)")
    else:
        print(f"📊 Storage Efficiency: {abs(size_change):.1f}% smaller")
    
    # Recommendations
    print(f"\n💡 KEY INSIGHTS:")
    print("-" * 15)
    
    if data_integrity:
        print("• ✅ WKB data integrity preserved perfectly")
    else:
        print("• ❌ WKB data integrity issues detected")
    
    if spatial_improvements and avg_spatial_improvement > 0:
        print(f"• ✅ GeoPage spatial optimization working ({avg_spatial_improvement:.1f}% faster)")
        
        # Analyze by selectivity
        selective_queries = [imp for i, imp in enumerate(spatial_improvements) 
                           if list(custom_spatial.values())[i]['selectivity'] < 0.1]
        if selective_queries:
            avg_selective = sum(selective_queries) / len(selective_queries)
            print(f"• 🎯 Best performance on selective queries ({avg_selective:.1f}% improvement)")
    
    if size_change > 10:
        print(f"• 📊 Spatial indexing adds {size_change:.1f}% storage overhead")
    
    if write_improvement < -10:
        print(f"• ⚠️ Write performance slower due to spatial processing ({abs(write_improvement):.1f}%)")
    
    return {
        'data_integrity': data_integrity,
        'avg_spatial_improvement': avg_spatial_improvement if spatial_improvements else 0,
        'write_improvement': write_improvement,
        'size_change': size_change,
        'spatial_improvements': spatial_improvements
    }

def main():
    if len(sys.argv) != 3:
        print("Usage: python compare_wkb_results.py <opensource_json> <custom_json>")
        print("Example: python compare_wkb_results.py test_results/uber_wkb_results_opensource_*.json test_results/uber_wkb_results_custom_*.json")
        sys.exit(1)
    
    opensource_file = sys.argv[1]
    custom_file = sys.argv[2]
    
    results = compare_wkb_results(opensource_file, custom_file)
    
    if results:
        print(f"\n🏁 FINAL VERDICT")
        print("=" * 20)
        
        if results['data_integrity'] and results['avg_spatial_improvement'] > 0:
            print(f"🎉 SUCCESS: GeoPage WKB implementation is working excellently!")
            print(f"   • Perfect data integrity")
            print(f"   • {results['avg_spatial_improvement']:.1f}% average spatial query improvement")
            print(f"   • Ready for production use")
        elif results['data_integrity']:
            print(f"🟡 PARTIAL SUCCESS: Data integrity perfect, performance neutral")
            print(f"   • WKB data preserved correctly")
            print(f"   • {results['avg_spatial_improvement']:.1f}% spatial performance change")
        else:
            print(f"❌ ISSUES DETECTED: Data integrity or performance problems")
            print(f"   • Data integrity: {'OK' if results['data_integrity'] else 'FAILED'}")
            print(f"   • Performance: {results['avg_spatial_improvement']:.1f}% change")

if __name__ == "__main__":
    main()
