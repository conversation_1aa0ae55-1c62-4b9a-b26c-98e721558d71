#!/usr/bin/env python3
"""
Test DuckDB spatial functions with our fixed WKB datasets
"""

import duckdb
import lance
import time

def test_duckdb_wkb_spatial():
    """Test DuckDB spatial functions with fixed WKB data."""
    print("🦆 TESTING DUCKDB SPATIAL WITH FIXED WKB DATA")
    print("=" * 60)
    
    # Setup DuckDB
    conn = duckdb.connect()
    conn.install_extension('spatial')
    conn.load_extension('spatial')
    
    # Test both datasets
    datasets = [
        ('test_datasets/wkb_fix_opensource', 'Open Source'),
        ('test_datasets/wkb_fix_custom', 'GeoPage Fixed')
    ]
    
    results = {}
    
    for dataset_path, name in datasets:
        print(f"\n🔍 Testing {name} dataset...")
        
        try:
            # Open dataset
            dataset = lance.dataset(dataset_path)
            
            # Register in DuckDB (small dataset, safe to load fully)
            sample_data = dataset.to_table().to_pandas()
            conn.register(f"dataset_{name.lower().replace(' ', '_')}", sample_data)
            
            table_name = f"dataset_{name.lower().replace(' ', '_')}"
            
            # Test 1: Basic WKB access
            wkb_count = conn.execute(f"SELECT COUNT(*) FROM {table_name} WHERE pickup_point_wkb IS NOT NULL").fetchone()[0]
            print(f"✅ Non-null WKB count: {wkb_count:,}")
            
            # Test 2: WKB length validation
            length_count = conn.execute(f"SELECT COUNT(*) FROM {table_name} WHERE octet_length(pickup_point_wkb) = 21").fetchone()[0]
            print(f"✅ Valid length WKB count: {length_count:,}")
            
            # Test 3: WKB to geometry conversion
            start_time = time.time()
            geom_count = conn.execute(f"""
                SELECT COUNT(*) FROM {table_name} 
                WHERE pickup_point_wkb IS NOT NULL 
                AND ST_GeomFromWKB(pickup_point_wkb) IS NOT NULL
            """).fetchone()[0]
            geom_time = time.time() - start_time
            print(f"✅ Valid geometry conversion: {geom_count:,} ({geom_time:.3f}s)")
            
            # Test 4: Spatial intersection query
            start_time = time.time()
            spatial_count = conn.execute(f"""
                SELECT COUNT(*) FROM {table_name} 
                WHERE pickup_point_wkb IS NOT NULL 
                AND octet_length(pickup_point_wkb) = 21 
                AND ST_Intersects(
                    ST_GeomFromWKB(pickup_point_wkb),
                    ST_MakeEnvelope(-73.99, 40.74, -73.94, 40.80)
                )
            """).fetchone()[0]
            spatial_time = time.time() - start_time
            print(f"✅ Spatial intersection: {spatial_count:,} results ({spatial_time:.3f}s)")
            
            # Test 5: Compare with coordinate-based query
            start_time = time.time()
            coord_count = conn.execute(f"""
                SELECT COUNT(*) FROM {table_name} 
                WHERE pickup_longitude >= -73.99 AND pickup_longitude <= -73.94
                AND pickup_latitude >= 40.74 AND pickup_latitude <= 40.80
            """).fetchone()[0]
            coord_time = time.time() - start_time
            print(f"✅ Coordinate query: {coord_count:,} results ({coord_time:.3f}s)")
            
            results[name] = {
                'wkb_count': wkb_count,
                'length_count': length_count,
                'geom_count': geom_count,
                'geom_time': geom_time,
                'spatial_count': spatial_count,
                'spatial_time': spatial_time,
                'coord_count': coord_count,
                'coord_time': coord_time,
                'success': True
            }
            
        except Exception as e:
            print(f"❌ Error testing {name}: {e}")
            results[name] = {'success': False, 'error': str(e)}
    
    # Compare results
    print(f"\n{'='*60}")
    print("📊 DUCKDB SPATIAL COMPARISON (FIXED WKB)")
    print(f"{'='*60}")
    
    if all(r.get('success', False) for r in results.values()):
        print(f"\n🗺️ WKB Spatial Function Results:")
        print(f"{'Metric':<25} {'Open Source':<15} {'GeoPage Fixed':<15} {'Status':<10}")
        print("-" * 70)
        
        os_result = results['Open Source']
        gp_result = results['GeoPage Fixed']
        
        metrics = [
            ('WKB Count', 'wkb_count'),
            ('Valid Length', 'length_count'),
            ('Geometry Conversion', 'geom_count'),
            ('Spatial Intersection', 'spatial_count'),
            ('Coordinate Query', 'coord_count'),
        ]
        
        for metric_name, key in metrics:
            os_val = os_result.get(key, 0)
            gp_val = gp_result.get(key, 0)
            status = "✅" if os_val == gp_val else "❌"
            print(f"{metric_name:<25} {os_val:<15,} {gp_val:<15,} {status:<10}")
        
        print(f"\n⚡ Performance Comparison:")
        print(f"{'Query Type':<25} {'Open Source':<15} {'GeoPage Fixed':<15} {'Improvement':<15}")
        print("-" * 75)
        
        perf_metrics = [
            ('Geometry Conversion', 'geom_time'),
            ('Spatial Intersection', 'spatial_time'),
            ('Coordinate Query', 'coord_time'),
        ]
        
        for metric_name, key in perf_metrics:
            os_time = os_result.get(key, 0)
            gp_time = gp_result.get(key, 0)
            if os_time > 0:
                improvement = ((os_time - gp_time) / os_time) * 100
                print(f"{metric_name:<25} {os_time:<15.3f} {gp_time:<15.3f} {improvement:+.1f}%")
            else:
                print(f"{metric_name:<25} {os_time:<15.3f} {gp_time:<15.3f} {'N/A':<15}")
        
        # Check if WKB spatial functions work
        if (gp_result.get('spatial_count', 0) > 0 and 
            gp_result.get('geom_count', 0) > 0):
            print(f"\n🎉 WKB SPATIAL FUNCTIONS WORKING!")
            print("✅ ST_GeomFromWKB() conversion successful")
            print("✅ ST_Intersects() spatial queries working")
            print("✅ GeoPage WKB data integrity preserved")
        else:
            print(f"\n❌ WKB spatial functions still not working")
    else:
        print("❌ One or more tests failed")
        for name, result in results.items():
            if not result.get('success', False):
                print(f"  {name}: {result.get('error', 'Unknown error')}")
    
    conn.close()
    return results

if __name__ == "__main__":
    results = test_duckdb_wkb_spatial()
    print("\n🎉 DuckDB WKB spatial testing complete!")
