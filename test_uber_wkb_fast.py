#!/usr/bin/env python3
"""
Fast Uber WKB Integration Test
Uses vectorized operations instead of slow row-by-row iteration.
"""

import os
import sys
import time
import struct
import lance
import pandas as pd
import numpy as np
from pathlib import Path

def create_wkb_point_vectorized(lon_series: pd.Series, lat_series: pd.Series) -> pd.Series:
    """Create WKB Point geometries using vectorized operations."""
    print("🔧 Creating WKB geometries (vectorized)...")
    
    # Filter valid coordinates
    valid_mask = (
        pd.notna(lon_series) & pd.notna(lat_series) &
        (lon_series != 0) & (lat_series != 0) &
        (lon_series >= -180) & (lon_series <= 180) &
        (lat_series >= -90) & (lat_series <= 90)
    )
    
    print(f"  📍 Valid coordinates: {valid_mask.sum():,}/{len(lon_series):,} ({valid_mask.sum()/len(lon_series)*100:.1f}%)")
    
    # Create WKB geometries for valid coordinates
    def create_single_wkb(lon: float, lat: float) -> bytes:
        """Create a single WKB Point geometry."""
        # WKB Point format: 
        # - 1 byte: byte order (1 = little endian)
        # - 4 bytes: geometry type (1 = Point)
        # - 8 bytes: X coordinate (longitude)
        # - 8 bytes: Y coordinate (latitude)
        return struct.pack('<BId d', 1, 1, lon, lat)
    
    # Apply vectorized WKB creation
    wkb_series = pd.Series([None] * len(lon_series), dtype=object)
    
    if valid_mask.sum() > 0:
        valid_lons = lon_series[valid_mask]
        valid_lats = lat_series[valid_mask]
        
        # Use numpy vectorization for speed
        wkb_geometries = [
            create_single_wkb(lon, lat) 
            for lon, lat in zip(valid_lons, valid_lats)
        ]
        
        wkb_series.loc[valid_mask] = wkb_geometries
    
    return wkb_series

def load_and_enhance_uber_data_fast(data_file: str, max_rows: int = None) -> pd.DataFrame:
    """Load Uber data and enhance with WKB geometries using fast vectorized operations."""
    print(f"📂 Loading Uber data from: {data_file}")
    
    # Load data
    df = pd.read_parquet(data_file)
    if max_rows and len(df) > max_rows:
        df = df.head(max_rows)
        print(f"📊 Loaded {len(df):,} rows (limited to {max_rows:,})")
    else:
        print(f"📊 Loaded {len(df):,} rows")
    
    print(f"📊 Columns: {list(df.columns)}")
    
    # Check for required spatial columns
    required_cols = ['pickup_longitude', 'pickup_latitude', 'dropoff_longitude', 'dropoff_latitude']
    available_cols = [col for col in required_cols if col in df.columns]
    print(f"🗺️ Available spatial columns: {available_cols}")
    
    if len(available_cols) < 4:
        print(f"❌ Missing required spatial columns. Need: {required_cols}")
        return None
    
    # Create WKB geometries using vectorized operations
    start_time = time.time()
    
    # Create pickup WKB geometries
    df['pickup_point_wkb'] = create_wkb_point_vectorized(
        df['pickup_longitude'], df['pickup_latitude']
    )
    
    # Create dropoff WKB geometries  
    df['dropoff_point_wkb'] = create_wkb_point_vectorized(
        df['dropoff_longitude'], df['dropoff_latitude']
    )
    
    wkb_time = time.time() - start_time
    
    # Count valid geometries
    pickup_count = df['pickup_point_wkb'].notna().sum()
    dropoff_count = df['dropoff_point_wkb'].notna().sum()
    
    print(f"✅ WKB creation completed in {wkb_time:.3f}s")
    print(f"📍 Pickup WKB: {pickup_count:,} geometries ({pickup_count/len(df)*100:.1f}%)")
    print(f"📍 Dropoff WKB: {dropoff_count:,} geometries ({dropoff_count/len(df)*100:.1f}%)")
    
    return df

def test_wkb_dataset_creation(mode: str, max_rows: int = 100000):
    """Test creating WKB dataset with specified mode."""
    print(f"\n🧪 Testing {mode.upper()} WKB dataset creation...")
    
    # Set environment for the mode
    if mode == 'custom':
        os.environ['LANCE_GEOPAGE_ENCODING_REQUESTED'] = '1'
        print("🎯 GeoPage encoding enabled")
    else:
        os.environ.pop('LANCE_GEOPAGE_ENCODING_REQUESTED', None)
        print("📊 Standard Lance encoding")
    
    # Load and enhance data
    data_file = "uber_data/nyc-taxi/2009/01/data.parquet"
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        return None
    
    df = load_and_enhance_uber_data_fast(data_file, max_rows)
    if df is None:
        return None
    
    # Create dataset path
    dataset_path = f"datasets/uber_wkb_{mode}_fast"
    
    try:
        # Remove existing dataset
        import shutil
        if os.path.exists(dataset_path):
            shutil.rmtree(dataset_path)
        
        os.makedirs(os.path.dirname(dataset_path), exist_ok=True)
        
        # Write dataset
        print(f"📝 Writing dataset to {dataset_path}...")
        start_time = time.time()
        
        if mode == 'custom':
            dataset = lance.write_dataset(df, dataset_path, encoding="geopage")
        else:
            dataset = lance.write_dataset(df, dataset_path)
        
        write_time = time.time() - start_time
        print(f"✅ Write completed in {write_time:.3f}s")
        
        # Test reading
        print("📖 Testing read...")
        start_time = time.time()
        
        # Read a sample
        sample = dataset.to_table(limit=10).to_pandas()
        read_time = time.time() - start_time
        
        print(f"✅ Read completed in {read_time:.3f}s")
        print(f"📊 Sample data shape: {sample.shape}")
        
        # Verify WKB data integrity
        pickup_wkb = sample['pickup_point_wkb'].iloc[0]
        if pickup_wkb is not None:
            print(f"📏 WKB length: {len(pickup_wkb)} bytes")
            print(f"🔢 WKB hex: {pickup_wkb[:20].hex()}")
            
            # Parse WKB to verify integrity
            if len(pickup_wkb) == 21:
                byte_order = pickup_wkb[0]
                wkb_type = struct.unpack('<I', pickup_wkb[1:5])[0]
                x = struct.unpack('<d', pickup_wkb[5:13])[0]
                y = struct.unpack('<d', pickup_wkb[13:21])[0]
                
                print(f"📐 Byte order: {byte_order} ({'Little' if byte_order == 1 else 'Big'} endian)")
                print(f"🎯 WKB type: {wkb_type} ({'Point' if wkb_type == 1 else 'Other'})")
                print(f"📍 Coordinates: ({x:.6f}, {y:.6f})")
                
                return {
                    'mode': mode,
                    'rows': len(df),
                    'write_time': write_time,
                    'read_time': read_time,
                    'wkb_length': len(pickup_wkb),
                    'wkb_type': wkb_type,
                    'coordinates': (x, y),
                    'success': True
                }
            else:
                print(f"❌ Invalid WKB length: {len(pickup_wkb)} (expected 21)")
                return {'mode': mode, 'success': False, 'error': 'Invalid WKB length'}
        else:
            print("❌ No WKB data found in sample")
            return {'mode': mode, 'success': False, 'error': 'No WKB data'}
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return {'mode': mode, 'success': False, 'error': str(e)}

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Fast Uber WKB integration testing")
    parser.add_argument("--mode", choices=["opensource", "custom"], default="custom",
                       help="Testing mode")
    parser.add_argument("--max-rows", type=int, default=100000,
                       help="Maximum rows to test")
    
    args = parser.parse_args()
    
    print("🚗" * 20)
    print("🔧 FAST UBER WKB INTEGRATION TEST")
    print("🚗" * 20)
    
    # Test the specified mode
    result = test_wkb_dataset_creation(args.mode, args.max_rows)
    
    if result and result.get('success'):
        print(f"\n🎉 SUCCESS! {args.mode.upper()} mode working:")
        print(f"  📊 Rows: {result['rows']:,}")
        print(f"  ⏱️ Write time: {result['write_time']:.3f}s")
        print(f"  ⏱️ Read time: {result['read_time']:.3f}s")
        print(f"  📏 WKB length: {result['wkb_length']} bytes")
        print(f"  🎯 WKB type: {result['wkb_type']} (Point)")
        print(f"  📍 Coordinates: {result['coordinates']}")
    else:
        print(f"\n❌ FAILED: {result.get('error', 'Unknown error') if result else 'No result'}")
    
    print("\n🎉 Fast WKB integration testing complete!")

if __name__ == "__main__":
    main()
