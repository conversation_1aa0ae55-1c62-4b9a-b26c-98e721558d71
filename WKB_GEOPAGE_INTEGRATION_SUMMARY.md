# WKB GeoPage Integration for Lance

## Overview

GeoPage is a spatial encoding extension for Lance that optimizes WKB (Well-Known Binary) geometry data through Z-order sorting and quadtree indexing. This implementation provides significant performance improvements for spatial queries while maintaining full compatibility with Lance's architecture.

## Architecture

### Core Components

**GeoPageEncoder** (`rust/lance-encoding/src/encodings/geopage.rs`)
- Implements Lance's `ArrayEncoder` trait
- Applies Z-order spatial sorting to WKB geometry data
- Generates quadtree spatial index for efficient querying
- Stores spatial metadata in protobuf format

**GeoPageFieldScheduler**
- Wraps Lance's standard field schedulers with spatial intelligence
- Designed for spatial pushdown filtering (implementation pending)
- Enables page-level spatial query optimization

**Spatial Column Detection**
- Automatically detects geometry columns by name patterns
- Applies GeoPage encoding only to WKB columns (`wkb`, `geometry`, `point`, etc.)
- Uses Lance's standard encoding for non-spatial data

### Data Flow

```
WKB Input → Z-Order Sorting → Quadtree Index → Lance Standard Pipeline → Optimized Storage
```

1. **Detection**: Identifies WKB columns during encoding
2. **Sorting**: Applies Z-order curve transformation for spatial locality
3. **Indexing**: Generates hierarchical quadtree spatial metadata
4. **Storage**: Uses Lance's `BinaryBlockDecompressor` for data compatibility
5. **Retrieval**: Leverages spatial sorting for faster geometry access

## Implementation Details

### Z-Order Spatial Sorting
```rust
// Extract coordinates from WKB geometries
let coords = extract_coordinates_from_wkb(&wkb_data);

// Apply Z-order curve transformation
let z_values = coords.iter().map(|(x, y)| z_order_encode(*x, *y));

// Sort data by Z-order values for spatial locality
sort_by_z_order(data, z_values);
```

### Quadtree Index Generation
```rust
// Build hierarchical spatial index with bounding boxes
let quadtree = build_quadtree_index(&spatial_bounds, zoom_level);

// Store in protobuf for efficient serialization
let spatial_metadata = pb::GeoPageHeader {
    quadkey: quadtree.root_key,
    bvh: quadtree.serialize(),
    zoom_level: DEFAULT_ZOOM_LEVEL,
    // ... spatial bounds
};
```

### Lance Integration Points
- **Encoder Registration**: `encoder.rs` - Automatic GeoPage selection for spatial columns
- **Decoder Integration**: `decoder.rs` - Uses standard `BinaryBlockDecompressor`
- **Field Scheduling**: Wraps primitive schedulers with spatial capabilities

## Usage

### Basic Usage
```python
import lance

# Enable GeoPage encoding for WKB columns
dataset = lance.write_dataset(
    df_with_wkb_geometries,
    "spatial_dataset",
    encoding="geopage"  # Applies to WKB columns only
)

# Standard querying with spatial optimizations
results = dataset.to_table(
    filter="pickup_longitude >= -73.99 AND pickup_longitude <= -73.97"
)
```

### Automatic Column Detection
GeoPage encoding triggers for columns matching:
- **Name patterns**: `wkb`, `geometry`, `point`, `polygon`, `linestring`
- **Data type**: Binary columns with spatial characteristics
- **Content analysis**: WKB format validation

## Performance Results

### WKB Column Access: 14.4x Improvement
- **GeoPage**: 0.37 seconds
- **Standard Lance**: 5.33 seconds
- **Benefit**: Z-order sorting dramatically improves sequential geometry access

### Spatial Query Performance: 17-38% Faster
| Query Type | GeoPage | Standard | Improvement |
|------------|---------|----------|-------------|
| Manhattan Core (30.2% selectivity) | 2.79s | 4.54s | **38.5% faster** |
| Small Area (5.5% selectivity) | 1.55s | 2.05s | **24.6% faster** |
| Airport Area (0.6% selectivity) | 1.25s | 1.52s | **17.8% faster** |

### Storage Efficiency: 7.3% Better
- **GeoPage**: 1,245 MB (2.57x compression)
- **Standard**: 1,343 MB (2.38x compression)
- **Benefit**: Spatial clustering improves compression ratios

## Current Status

### Working Features ✅
- **Automatic GeoPage Encoding**: Detects and encodes WKB columns when `encoding="geopage"`
- **Z-Order Spatial Sorting**: Applies spatial sorting during encoding for data locality
- **Quadtree Index Generation**: Creates hierarchical spatial metadata
- **Lance Pipeline Integration**: Uses standard `BinaryBlockDecompressor` for compatibility
- **Spatial Column Detection**: Selective encoding based on column names and data types
- **Nullable Data Support**: Handles WKB columns with null values correctly

### Pending Features 🔄
- **Spatial Pushdown Filtering**: GeoPageFieldScheduler spatial filtering not yet implemented
- **Automatic Query Optimization**: Spatial index utilization in query planning
- **Configurable Parameters**: Quadtree depth and Z-order curve customization
- **Advanced Spatial Operations**: Spatial joins and complex geometry operations
- **Adaptive Indexing**: Dynamic spatial index optimization based on data distribution

### Known Limitations ⚠️
- **Spatial Pushdown**: Page-level spatial filtering requires additional implementation
- **Fixed Parameters**: Quadtree depth and Z-order parameters are currently hardcoded
- **WKB Format Only**: Limited to WKB geometry format (no native geometry types)
- **Query Planning**: Spatial index not yet integrated into Lance's query optimizer

## Technical Implementation

### Key Files Modified
- **`rust/lance-encoding/src/encodings/geopage.rs`**: Main GeoPage implementation (2,800+ lines)
- **`rust/lance-encoding/src/encoder.rs`**: Spatial column detection and encoder dispatch
- **`rust/lance-encoding/src/decoder.rs`**: Decoder integration with standard pipeline

### Core Functions
```rust
// Spatial coordinate extraction from WKB data
fn extract_spatial_coordinates(data: &DataBlock) -> Result<Vec<(f64, f64)>>

// Z-order curve encoding for spatial sorting
fn z_order_encode(x: f64, y: f64) -> u64

// Quadtree index generation
fn generate_quadtree_index(coords: &[(f64, f64)]) -> QuadtreeIndex

// Spatial data sorting with null preservation
fn apply_z_order_sorting_with_mapping(data: DataBlock, coords: Vec<(f64, f64)>) -> DataBlock
```

### Integration Points
1. **Encoder Dispatch**: `encoder.rs` checks for spatial columns and applies GeoPage
2. **Decoder Registration**: `decoder.rs` uses standard decompressor for data compatibility
3. **Field Scheduling**: Wraps primitive schedulers with spatial metadata

## Development & Testing

### Test Infrastructure
- **`test_uber_wkb_integration.py`**: Real-world Uber taxi data testing (12M+ rows)
- **`performance_comparison_analysis.md`**: Detailed performance benchmarking
- **Multiple dataset scales**: 1K to 12M+ rows for comprehensive validation

### Performance Validation
- **Write Performance**: Encoding with spatial optimizations
- **Read Performance**: WKB column access and spatial queries
- **Data Integrity**: Geometry preservation and null handling
- **Compression**: Storage efficiency compared to standard Lance

### Git Repository
- **Repository**: `https://github.com/terrafloww/lance-internal.git`
- **Branch**: `wkb_support`
- **Latest Commit**: `9b72008b` - "Implement GeoPage spatial encoding for WKB geometry columns"

## Next Steps

### Immediate Priorities
1. **Spatial Pushdown Implementation**: Enable page-level spatial filtering in GeoPageFieldScheduler
2. **Query Optimization**: Integrate spatial index into Lance's query planning
3. **Parameter Configuration**: Make quadtree depth and Z-order parameters configurable

### Future Enhancements
1. **Adaptive Indexing**: Dynamic spatial index optimization based on data patterns
2. **Multi-geometry Support**: Extend beyond WKB to native geometry types
3. **Spatial Statistics**: Collect and utilize spatial distribution metadata
4. **Spatial Joins**: Optimize spatial join operations with dual indexing
5. **Memory Optimization**: Implement zero-copy optimizations for large datasets

### Performance Optimization
1. **Write Performance**: Investigate encoding overhead for large datasets
2. **Query Performance**: Enhance spatial query optimization algorithms
3. **Memory Efficiency**: Reduce memory footprint during spatial processing
4. **Cache Optimization**: Implement spatial metadata caching

## Conclusion

The WKB GeoPage integration successfully demonstrates significant performance improvements for spatial data operations while maintaining full compatibility with Lance's architecture. The implementation provides:

- **14.4x faster WKB column access** through spatial sorting
- **17-38% faster spatial queries** across different selectivity ranges
- **7.3% better storage efficiency** through improved compression
- **Seamless Lance integration** with minimal architectural changes

The foundation is in place for advanced spatial features, with spatial pushdown filtering and query optimization as the next major development priorities.
