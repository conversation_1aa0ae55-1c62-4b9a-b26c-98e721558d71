#!/usr/bin/env python3
"""
Real-world A/B testing with Uber/NYC Taxi data.
Compares Open Source Lance vs Custom Lance with GeoPage encoding
using actual taxi trip data for realistic performance benchmarks.
"""

import os
import sys
import time
import json
import argparse
import pandas as pd
import pyarrow as pa
import lance
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Any

class UberABTester:
    def __init__(self, mode: str = "opensource"):
        self.mode = mode
        self.results = {}
        self.temp_dir = None
        
    def load_uber_data(self, file_path: str, max_rows: int = None) -> pd.DataFrame:
        """Load Uber data from parquet file."""
        print(f"📊 Loading data from: {file_path}")
        
        if max_rows:
            # Read with row limit for testing
            df = pd.read_parquet(file_path).head(max_rows)
        else:
            df = pd.read_parquet(file_path)
        
        print(f"✅ Loaded {len(df):,} rows, {len(df.columns)} columns")
        return df
    
    def analyze_spatial_bounds(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze spatial bounds of the data."""
        spatial_analysis = {}
        
        # Focus on valid NYC coordinates
        pickup_mask = (
            (df['pickup_longitude'].between(-75, -73)) &
            (df['pickup_latitude'].between(40.5, 41.0)) &
            (df['pickup_longitude'].notna()) &
            (df['pickup_latitude'].notna())
        )
        
        dropoff_mask = (
            (df['dropoff_longitude'].between(-75, -73)) &
            (df['dropoff_latitude'].between(40.5, 41.0)) &
            (df['dropoff_longitude'].notna()) &
            (df['dropoff_latitude'].notna())
        )
        
        valid_pickup = df[pickup_mask]
        valid_dropoff = df[dropoff_mask]
        
        spatial_analysis = {
            'total_rows': len(df),
            'valid_pickup_rows': len(valid_pickup),
            'valid_dropoff_rows': len(valid_dropoff),
            'pickup_validity_rate': len(valid_pickup) / len(df),
            'dropoff_validity_rate': len(valid_dropoff) / len(df),
            
            'pickup_bounds': {
                'min_lon': float(valid_pickup['pickup_longitude'].min()),
                'max_lon': float(valid_pickup['pickup_longitude'].max()),
                'min_lat': float(valid_pickup['pickup_latitude'].min()),
                'max_lat': float(valid_pickup['pickup_latitude'].max()),
            } if len(valid_pickup) > 0 else None,
            
            'dropoff_bounds': {
                'min_lon': float(valid_dropoff['dropoff_longitude'].min()),
                'max_lon': float(valid_dropoff['dropoff_longitude'].max()),
                'min_lat': float(valid_dropoff['dropoff_latitude'].min()),
                'max_lat': float(valid_dropoff['dropoff_latitude'].max()),
            } if len(valid_dropoff) > 0 else None,
        }
        
        return spatial_analysis
    
    def create_spatial_queries(self, spatial_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create realistic spatial queries based on data bounds."""
        queries = []
        
        if spatial_analysis['pickup_bounds']:
            bounds = spatial_analysis['pickup_bounds']
            
            # Manhattan core area (high density)
            queries.append({
                'name': 'Manhattan Core',
                'description': 'High-density Manhattan area',
                'filter': (
                    f"pickup_longitude >= -74.01 AND pickup_longitude <= -73.97 AND "
                    f"pickup_latitude >= 40.74 AND pickup_latitude <= 40.78"
                ),
                'expected_selectivity': 0.6  # ~60% of trips
            })
            
            # Midtown area (medium density)
            queries.append({
                'name': 'Midtown Area',
                'description': 'Midtown Manhattan business district',
                'filter': (
                    f"pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND "
                    f"pickup_latitude >= 40.75 AND pickup_latitude <= 40.77"
                ),
                'expected_selectivity': 0.3  # ~30% of trips
            })
            
            # Small area (low density)
            queries.append({
                'name': 'Small Area',
                'description': 'Small specific area',
                'filter': (
                    f"pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND "
                    f"pickup_latitude >= 40.755 AND pickup_latitude <= 40.765"
                ),
                'expected_selectivity': 0.1  # ~10% of trips
            })
            
            # Airport area (JFK vicinity)
            queries.append({
                'name': 'Airport Area',
                'description': 'JFK Airport vicinity',
                'filter': (
                    f"dropoff_longitude >= -73.82 AND dropoff_longitude <= -73.76 AND "
                    f"dropoff_latitude >= 40.63 AND dropoff_latitude <= 40.67"
                ),
                'expected_selectivity': 0.05  # ~5% of trips
            })
        
        return queries

    def create_bbox_queries(self, spatial_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create bbox format queries for GeoPage spatial optimization testing."""
        bbox_queries = []

        if spatial_analysis['pickup_bounds']:
            # For now, use the same SQL format but mark them as bbox tests
            # TODO: Once GeoPage bbox filter format is implemented, update these
            bbox_queries.append({
                'name': 'Manhattan Core BBOX',
                'description': 'High-density Manhattan area (testing spatial optimization)',
                'bbox_filter': (
                    f"pickup_longitude >= -74.01 AND pickup_longitude <= -73.97 AND "
                    f"pickup_latitude >= 40.74 AND pickup_latitude <= 40.78"
                ),
            })

            # Midtown area bbox
            bbox_queries.append({
                'name': 'Midtown Area BBOX',
                'description': 'Midtown Manhattan (testing spatial optimization)',
                'bbox_filter': (
                    f"pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND "
                    f"pickup_latitude >= 40.75 AND pickup_latitude <= 40.77"
                ),
            })

            # Small area bbox
            bbox_queries.append({
                'name': 'Small Area BBOX',
                'description': 'Small specific area (testing spatial optimization)',
                'bbox_filter': (
                    f"pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND "
                    f"pickup_latitude >= 40.755 AND pickup_latitude <= 40.765"
                ),
            })

        return bbox_queries

    def apply_spatial_presorting(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply spatial pre-sorting using Z-order (Morton curve) before writing to Lance."""
        print("🔄 Applying spatial pre-sorting using Z-order curve...")

        # Filter to valid spatial coordinates for sorting
        valid_mask = (
            (df['pickup_longitude'].between(-75, -73)) &
            (df['pickup_latitude'].between(40.5, 41.0)) &
            (df['pickup_longitude'].notna()) &
            (df['pickup_latitude'].notna())
        )

        # Separate valid and invalid rows
        valid_df = df[valid_mask].copy()
        invalid_df = df[~valid_mask].copy()

        if len(valid_df) == 0:
            print("  ⚠️ No valid spatial coordinates found, returning original data")
            return df

        # Calculate Morton codes (Z-order) for spatial sorting
        # Normalize coordinates to [0, 1] range for Morton encoding
        lon_min, lon_max = valid_df['pickup_longitude'].min(), valid_df['pickup_longitude'].max()
        lat_min, lat_max = valid_df['pickup_latitude'].min(), valid_df['pickup_latitude'].max()

        # Avoid division by zero
        lon_range = max(lon_max - lon_min, 1e-10)
        lat_range = max(lat_max - lat_min, 1e-10)

        # Normalize to [0, 1]
        norm_lon = (valid_df['pickup_longitude'] - lon_min) / lon_range
        norm_lat = (valid_df['pickup_latitude'] - lat_min) / lat_range

        # Simple Morton code calculation (interleave bits)
        # Scale to integer range for bit operations
        SCALE = 1000000  # 6 decimal places precision
        int_lon = (norm_lon * SCALE).astype(int)
        int_lat = (norm_lat * SCALE).astype(int)

        # Calculate Morton codes by interleaving bits
        morton_codes = []
        for lon, lat in zip(int_lon, int_lat):
            morton = 0
            for i in range(20):  # 20 bits each (40 bits total)
                morton |= (lon & (1 << i)) << i
                morton |= (lat & (1 << i)) << (i + 1)
            morton_codes.append(morton)

        # Add Morton codes to dataframe and sort
        valid_df['_morton_code'] = morton_codes
        valid_df_sorted = valid_df.sort_values('_morton_code').drop('_morton_code', axis=1)

        # Combine sorted valid data with invalid data (invalid data at the end)
        result_df = pd.concat([valid_df_sorted, invalid_df], ignore_index=True)

        print(f"  ✅ Sorted {len(valid_df):,} valid spatial rows using Z-order curve")
        print(f"  📊 Spatial locality improvement: {len(valid_df)/len(df):.1%} of data spatially sorted")

        return result_df

    def run_performance_test(self, df: pd.DataFrame, spatial_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Run comprehensive performance tests."""
        print(f"\n🧪 Running performance tests in {self.mode.upper()} mode...")
        
        results = {
            'mode': self.mode,
            'data_info': {
                'rows': len(df),
                'columns': len(df.columns),
                'memory_mb': df.memory_usage(deep=True).sum() / (1024 * 1024),
            },
            'spatial_analysis': spatial_analysis,
        }
        
        # Create persistent dataset path for DuckDB testing
        dataset_path = Path(f"datasets/taxi_{self.mode}")
        dataset_path.parent.mkdir(exist_ok=True)

        # Remove existing dataset if it exists
        if dataset_path.exists():
            shutil.rmtree(dataset_path)

        # Test 1: Write Performance
        print("📝 Testing write performance...")

        # NEW: Handle spatial pre-sorting for "presorted" mode
        if self.mode == "presorted":
            print("🔄 Pre-sorting data spatially before writing...")
            df_to_write = self.apply_spatial_presorting(df)
            print("🔧 Using standard Lance with spatially pre-sorted data...")
        else:
            df_to_write = df

        write_start = time.time()

        if self.mode == "custom":
            # Use GeoPage encoding for spatial columns - NEW IMPLEMENTATION
            print("🔧 Using custom Lance with GeoPage encoding...")

            # Apply GeoPage encoding to the entire dataset
            for col in ['pickup_longitude', 'pickup_latitude', 'dropoff_longitude', 'dropoff_latitude']:
                if col in df_to_write.columns:
                    print(f"  🗺️ Will apply GeoPage encoding to {col}")

            dataset = lance.write_dataset(df_to_write, dataset_path, encoding="geopage")
            print("✅ GeoPage encoding applied to spatial columns")
        elif self.mode == "presorted":
            # Standard Lance encoding with pre-sorted data
            print("🔧 Writing spatially pre-sorted data with standard Lance...")
            dataset = lance.write_dataset(df_to_write, dataset_path)
            print("✅ Standard Lance encoding applied to spatially sorted data")
        else:
            # Standard Lance encoding
            print("🔧 Using standard open-source Lance encoding...")
            dataset = lance.write_dataset(df_to_write, dataset_path)
            print("✅ Standard Lance encoding applied")

        write_time = time.time() - write_start

        # Get dataset size
        dataset_size = sum(f.stat().st_size for f in dataset_path.rglob('*') if f.is_file())
        dataset_size_mb = dataset_size / (1024 * 1024)

        results['write_performance'] = {
            'time_seconds': write_time,
            'dataset_size_mb': dataset_size_mb,
            'compression_ratio': results['data_info']['memory_mb'] / dataset_size_mb,
            'write_throughput_mb_per_sec': dataset_size_mb / write_time,
        }

        print(f"✅ Write: {write_time:.3f}s, {dataset_size_mb:.2f}MB")
        print(f"📁 Dataset saved to: {dataset_path}")

        # Test 2: Full Scan Performance
        print("📖 Testing full scan performance...")
        scan_start = time.time()
        full_data = dataset.to_table().to_pandas()
        scan_time = time.time() - scan_start

        results['full_scan_performance'] = {
            'time_seconds': scan_time,
            'rows_scanned': len(full_data),
            'scan_throughput_rows_per_sec': len(full_data) / scan_time,
        }

        print(f"✅ Full scan: {scan_time:.3f}s, {len(full_data):,} rows")

        # Test 3: Spatial Query Performance (Standard SQL filters)
        print("🗺️ Testing spatial query performance...")
        spatial_queries = self.create_spatial_queries(spatial_analysis)
        query_results = []

        for query in spatial_queries:
            query_start = time.time()
            try:
                filtered_data = dataset.to_table(filter=query['filter']).to_pandas()
                query_time = time.time() - query_start

                query_result = {
                    'name': query['name'],
                    'description': query['description'],
                    'filter': query['filter'],
                    'time_seconds': query_time,
                    'rows_returned': len(filtered_data),
                    'selectivity': len(filtered_data) / len(df),
                    'query_throughput_rows_per_sec': len(df) / query_time,
                }

                query_results.append(query_result)
                print(f"  ✅ {query['name']}: {query_time:.3f}s, {len(filtered_data):,} rows")

            except Exception as e:
                print(f"  ❌ {query['name']}: Error - {e}")
                query_results.append({
                    'name': query['name'],
                    'error': str(e)
                })

        results['spatial_query_performance'] = query_results

        # Test 3b: NEW - Spatial BBOX Filter Performance (GeoPage specific)
        if self.mode == "custom":
            print("🎯 Testing GeoPage bbox filter performance...")
            bbox_queries = self.create_bbox_queries(spatial_analysis)
            bbox_results = []

            for bbox_query in bbox_queries:
                bbox_start = time.time()
                try:
                    # Use bbox filter format that should trigger GeoPage optimization
                    bbox_filter = bbox_query['bbox_filter']
                    filtered_data = dataset.to_table(filter=bbox_filter).to_pandas()
                    bbox_time = time.time() - bbox_start

                    bbox_result = {
                        'name': bbox_query['name'],
                        'description': bbox_query['description'],
                        'bbox_filter': bbox_filter,
                        'time_seconds': bbox_time,
                        'rows_returned': len(filtered_data),
                        'selectivity': len(filtered_data) / len(df),
                        'bbox_throughput_rows_per_sec': len(df) / bbox_time,
                    }

                    bbox_results.append(bbox_result)
                    print(f"  🎯 {bbox_query['name']}: {bbox_time:.3f}s, {len(filtered_data):,} rows (bbox)")

                except Exception as e:
                    print(f"  ❌ {bbox_query['name']}: Error - {e}")
                    bbox_results.append({
                        'name': bbox_query['name'],
                        'error': str(e)
                    })

            results['bbox_query_performance'] = bbox_results
        else:
            results['bbox_query_performance'] = []

        # Test 4: Column Selection Performance
        print("📊 Testing column selection performance...")
        column_start = time.time()
        spatial_columns = ['pickup_longitude', 'pickup_latitude', 'dropoff_longitude', 'dropoff_latitude']
        column_data = dataset.to_table(columns=spatial_columns).to_pandas()
        column_time = time.time() - column_start

        results['column_selection_performance'] = {
            'time_seconds': column_time,
            'columns_selected': len(spatial_columns),
            'rows_returned': len(column_data),
            'data_size_mb': column_data.memory_usage(deep=True).sum() / (1024 * 1024),
        }

        print(f"✅ Column selection: {column_time:.3f}s, {len(spatial_columns)} cols")

        # Test 5: NEW - Gap Analysis Validation Tests
        if self.mode == "custom":
            print("🎯 Running gap analysis validation tests...")
            gap_analysis_results = self.run_gap_analysis_validation(dataset, df)
            results['gap_analysis_validation'] = gap_analysis_results
        else:
            results['gap_analysis_validation'] = {}

        return results

    def run_gap_analysis_validation(self, dataset, df: pd.DataFrame) -> Dict[str, Any]:
        """Run the 4-step validation checklist from gap analysis."""
        validation_results = {}

        # Validation 1: handles.len() < total_pages on a bbox query (proves pruning executed)
        print("  🔍 Test 1: Validating spatial pruning reduces page handles...")
        try:
            # Use SQL format for now until bbox format is implemented
            bbox_start = time.time()
            bbox_filter = "pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.75 AND pickup_latitude <= 40.77"
            bbox_data = dataset.to_table(filter=bbox_filter).to_pandas()
            bbox_time = time.time() - bbox_start

            validation_results['spatial_pruning_test'] = {
                'bbox_query_time': bbox_time,
                'rows_returned': len(bbox_data),
                'selectivity': len(bbox_data) / len(df),
                'status': 'measured_proxy'  # Can't directly measure page handles yet
            }
            print(f"    ✅ Bbox query: {bbox_time:.3f}s, {len(bbox_data):,} rows ({len(bbox_data)/len(df):.1%} selectivity)")
        except Exception as e:
            validation_results['spatial_pruning_test'] = {'error': str(e)}
            print(f"    ❌ Bbox query failed: {e}")

        # Validation 2: Full scan time parity with open-source Lance (±5%)
        print("  🔍 Test 2: Measuring full scan performance...")
        try:
            scan_start = time.time()
            full_data = dataset.to_table().to_pandas()
            scan_time = time.time() - scan_start

            validation_results['full_scan_parity_test'] = {
                'scan_time': scan_time,
                'rows_scanned': len(full_data),
                'throughput_rows_per_sec': len(full_data) / scan_time,
                'status': 'measured'
            }
            print(f"    ✅ Full scan: {scan_time:.3f}s, {len(full_data):,} rows")
        except Exception as e:
            validation_results['full_scan_parity_test'] = {'error': str(e)}
            print(f"    ❌ Full scan failed: {e}")

        # Validation 3: Midtown bbox performance target
        print("  🔍 Test 3: Measuring Midtown bbox performance...")
        try:
            midtown_start = time.time()
            midtown_filter = "pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND pickup_latitude >= 40.75 AND pickup_latitude <= 40.77"
            midtown_data = dataset.to_table(filter=midtown_filter).to_pandas()
            midtown_time = time.time() - midtown_start

            validation_results['midtown_bbox_test'] = {
                'query_time': midtown_time,
                'rows_returned': len(midtown_data),
                'selectivity': len(midtown_data) / len(df),
                'throughput_rows_per_sec': len(df) / midtown_time,
                'status': 'measured'
            }
            print(f"    ✅ Midtown bbox: {midtown_time:.3f}s, {len(midtown_data):,} rows")
        except Exception as e:
            validation_results['midtown_bbox_test'] = {'error': str(e)}
            print(f"    ❌ Midtown bbox failed: {e}")

        # Validation 4: Dataset size and compression analysis
        print("  🔍 Test 4: Analyzing dataset size and compression...")
        try:
            # This is already captured in write_performance, but let's add specific analysis
            validation_results['compression_analysis'] = {
                'status': 'measured_in_write_performance',
                'note': 'GeoPage should maintain similar compression while adding spatial index'
            }
            print(f"    ✅ Compression analysis captured in write performance")
        except Exception as e:
            validation_results['compression_analysis'] = {'error': str(e)}

        return validation_results
    
    def save_results(self, results: Dict[str, Any], output_dir: str = "test_results"):
        """Save test results to JSON file."""
        os.makedirs(output_dir, exist_ok=True)
        timestamp = int(time.time())
        filename = f"uber_ab_results_{self.mode}_{timestamp}.json"
        filepath = Path(output_dir) / filename
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"💾 Results saved to: {filepath}")
        return filepath

def main():
    parser = argparse.ArgumentParser(description="Uber data A/B testing for Lance GeoPage")
    parser.add_argument("--mode", choices=["opensource", "custom", "presorted"], default="opensource",
                       help="Testing mode: opensource, custom Lance, or presorted (spatial pre-sorting)")
    parser.add_argument("--data-file", type=str, 
                       default="uber_data/nyc-taxi/2009/01/data.parquet",
                       help="Path to Uber data parquet file")
    parser.add_argument("--max-rows", type=int, default=None,
                       help="Maximum rows to test (for quick testing)")
    
    args = parser.parse_args()
    
    print("=" * 80)
    print(f"🚗 UBER DATA A/B TESTING - {args.mode.upper()} MODE")
    print("=" * 80)
    
    # Initialize tester
    tester = UberABTester(mode=args.mode)
    
    # Load data
    if not os.path.exists(args.data_file):
        print(f"❌ Data file not found: {args.data_file}")
        sys.exit(1)
    
    df = tester.load_uber_data(args.data_file, max_rows=args.max_rows)
    
    # Analyze spatial properties
    print("\n🗺️ Analyzing spatial properties...")
    spatial_analysis = tester.analyze_spatial_bounds(df)
    print(f"✅ Spatial analysis complete:")
    print(f"  📊 Valid pickup locations: {spatial_analysis['valid_pickup_rows']:,} ({spatial_analysis['pickup_validity_rate']:.1%})")
    print(f"  📊 Valid dropoff locations: {spatial_analysis['valid_dropoff_rows']:,} ({spatial_analysis['dropoff_validity_rate']:.1%})")
    
    # Run performance tests
    results = tester.run_performance_test(df, spatial_analysis)
    
    # Save results
    results_file = tester.save_results(results)
    
    # Print summary
    print("\n" + "=" * 80)
    print(f"📊 SUMMARY ({args.mode.upper()})")
    print("=" * 80)
    print(f"📝 Write: {results['write_performance']['time_seconds']:.3f}s, {results['write_performance']['dataset_size_mb']:.2f}MB")
    print(f"📖 Full scan: {results['full_scan_performance']['time_seconds']:.3f}s")
    print(f"🗺️ Spatial queries: {len([q for q in results['spatial_query_performance'] if 'error' not in q])} successful")
    print(f"📊 Column selection: {results['column_selection_performance']['time_seconds']:.3f}s")

    # NEW: Summary for GeoPage-specific tests
    if args.mode == "custom":
        bbox_successful = len([q for q in results.get('bbox_query_performance', []) if 'error' not in q])
        print(f"🎯 Bbox queries: {bbox_successful} successful")

        gap_tests = results.get('gap_analysis_validation', {})
        gap_successful = len([test for test in gap_tests.values() if isinstance(test, dict) and 'error' not in test])
        print(f"✅ Gap analysis validation: {gap_successful}/4 tests successful")

        # Show key performance metrics for comparison
        if 'midtown_bbox_test' in gap_tests and 'error' not in gap_tests['midtown_bbox_test']:
            midtown_time = gap_tests['midtown_bbox_test']['query_time']
            print(f"🎯 Midtown bbox performance: {midtown_time:.3f}s")

    print(f"💾 Results saved to: test_results/uber_ab_results_{args.mode}_{int(time.time())}.json")
    
    return results

if __name__ == "__main__":
    results = main()
