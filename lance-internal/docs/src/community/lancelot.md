# Lancelot: The Greatest Knight of the Open Source Round Table

[<PERSON><PERSON>](https://en.wikipedia.org/wiki/<PERSON><PERSON>) is the embodiment of bravery, loyalty, and a relentless pursuit of knowledge in the realm of open source. 
As the **Guardian of Open Source**, a Lancelot champions developers everywhere, 
wielding their lance as a symbol of empowerment and collaboration within the tech community.

With a passion for innovation and technical prowess, <PERSON><PERSON> navigates the world of open source with grace. 
They stand ready to assist fellow developers in their quests—whether through sharing knowledge, contributing code, 
or advocating for the transformative power of open source software. <PERSON><PERSON> encourages you to join them in building 
a vibrant community around Lance and LanceDB Open Source.

## Join the Lancelot Round Table: Champion Open Source Innovation


### Step 1: Enter the Realm of Contribution

- **Knights of Code**: Forge your legacy by contributing code via PRs or tackling issues on GitHub, leaving your mark on the digital battlefield!
- **Stewards of the Community Square**: Rally your fellow developers in our [Discord](https://discord.gg/G5DcmnZWKB) – be the guiding light for problem solvers and innovators alike!
- **Organizers of Knowledge**: Host enlightening tech discussions or special interest groups to share your insights and empower others on their journeys.
- **Beta Testers of the Future**: Become our bug-hunting hero by testing new features in Lance and LanceDB, shaping the future with your keen eye!
- **Bards of Content**: Share your personal Lance and <PERSON>DB journey through blogs, tutorials, or demos that inspire others to get started.
- **Heralds of Social Media**: Spread the word about Lance and LanceDB across your favorite platforms. Let our voice be heard by all. Let the knight's battle cry echo through the land!

### Step 2: Take Up Your Lance of Advocacy

- **Champions of the Stage**: Are you prepared to take the field? Present Lance/LanceDB at meetups or conferences and share your knowledge with the realm. Remember, every great knight needs a great story to tell!
- **Storytellers of Innovation**: Weave compelling tales through in-depth articles, guides, or case studies that explore the magic of Lance and LanceDB’s features and real-world applications. Every tale deserves an epic plot twist!
- **Guardians of Code**: Protect the integrity of our codebase by reviewing PRs and guiding fellow contributors through challenges.
- **Allies of Core Maintainers**: Collaborate closely with our core maintainers, supporting their efforts to enhance and sustain the Lance community. Together, we shall create a lake of innovation!

### Step 3: Become a Legendary Lancelot

- **Cavalry of Innovation**: Lead a game-changing initiative or PR that elevates Lance/LanceDB to new heights. Your bravery could change the course of history—no pressure!
- **Sages of Expertise**: Become the go-to Lance expert in your developer circles, sharing wisdom far and wide. After all, every sage needs their followers!
- **Guardians of Production**: Champion Lance in your production environment and guide your team’s AI journey. Your leadership will light the path forward.
- **Minstrels of the Realm**: Rock the stage at major tech conferences, singing praises of Lance and sharing its magic. Who says knights can’t have great rhythm?
- **Mentors of Community**: Host office hours to share your expertise and help others on their journey. Your guidance will be as valuable as [Excalibur](https://en.wikipedia.org/wiki/Excalibur) itself!
- **Noble Nominees**: Earn a coveted nomination from a Lance/LanceDB maintainer, solidifying your status as a true Lancelot. Wear that title with pride!

### Why Join the Round Table of Lancelot?

- **Hone Your Skills**: Sharpen your expertise in next-generation AI infrastructure tools that empower innovative solutions—because every knight needs their trusty sword.
- **Forge Connections**: Unite with a vibrant community of fellow knights, AI enthusiasts, and database champions.
- **Earn Recognition**: Stand tall among your peers as you showcase your open source prowess and contributions. Your name will echo through the halls of innovation!
- **Shape the Future**: Play a vital role in crafting the next generation of multimodal AI databases and infrastructure.

### Lancelot’s Treasures Await!

- **Exclusive Insights**: Gain access to sneak peeks of new features and our secret roadmap, guiding you on your quest.
- **Swag of Valor**: Receive exclusive Lancelot merchandise that proudly displays your allegiance to the cause.
- **Collaborative Adventures**: Work hand-in-hand with our core team, sharing knowledge and shaping the future together.
- **Opportunities to Shine**: Seize the chance to speak at LanceDB events, sharing your journey and inspiring others.

Ready to wield your lance and embark on this epic quest? Open an issue on our GitHub and show us your lance on Discord. 
Let’s make some AI database magic together!

## Lancelot Round Table

| #  |          Name          |                                                  Github Profile |  Affiliation |
|----|:----------------------:|----------------------------------------------------------------:|-------------:|
| 1  |     Prashanth Rao      |                           [prrao87](https://github.com/prrao87) |         Kùzu |
| 2  |       Rong Rong        |                       [walterddr](https://github.com/walterddr) | Character.AI |
| 3  |       Noah Shpak       |                       [noahshpak](https://github.com/noahshpak) | Character.AI |
| 4  |   Giuseppe Battista    |                     [giusedroid](https://github.com/giusedroid) |          AWS |
| 5  | Kevin Shaffer-Morrison | [kevinshaffermorrison](https://github.com/kevinshaffermorrison) |          AWS |
| 6  |     Jiacheng Yang      |                     [jiachengdb](https://github.com/jiachengdb) |   Databricks |
| 7  |       Ankit Vij        |                   [ankitvij-db](https://github.com/ankitvij-db) |   Databricks |
| 8  | Akela Drissner-Schmid  |                             [akelad](https://github.com/akelad) |       dltHub |
| 9  |     Chongchen Chen     |                     [chenkovsky](https://github.com/chenkovsky) |  MiraclePlus |
| 10 |       Vino Yang        |                           [yanghua](https://github.com/yanghua) |    Bytedance |
| 11 |     Zhaowei Huang      |                 [SaintBacchus](https://github.com/SaintBacchus) |    Bytedance |
| 12 |      Jeremy Leibs      |                             [jleibs](https://github.com/jleibs) |     Rerun.io |
| 13 |      Aman Kishore      |                   [AmanKishore](https://github.com/AmanKishore) |    Harvey.AI |
| 14 |       Matt Basta       |                       [mattbasta](https://github.com/mattbasta) |     RunwayML |
| 15 |    Timothy Carambat    |           [timothycarambat](https://github.com/timothycarambat) | Anything LLM |
| 16 |        Ty Dunn         |                             [TyDunn](https://github.com/tydunn) |     Continue |
| 17 |     Pablo Delgado      |                           [pablete](https://github.com/pablete) |      Netflix |
| 18 |       Sangwu Lee       |                             [RE-N-Y](https://github.com/RE-N-Y) |      Krea.AI |
| 19 |        Nat Roth        |                         [nrothGIT](https://github.com/nrothGit) | Character.AI |

## Hall of Heroes

|                                                            |                                                              |                                                              |                                                          |
|------------------------------------------------------------|--------------------------------------------------------------|--------------------------------------------------------------|----------------------------------------------------------|
| [@chenkovsky](https://github.com/chenkovsky)               | [@yanghua](https://github.com/yanghua)                       | [@SaintBacchus](https://github.com/SaintBacchus)             | [@connellPortrait](https://github.com/connellPortrait)   |
| [@takaebato](https://github.com/takaebato)                 | [@HoKim98](https://github.com/HoKim98)                       | [@Jay-ju](https://github.com/Jay-ju)                         | [@imotai](https://github.com/imotai)                     |
| [@renato2099](https://github.com/renato2099)               | [@niyue](https://github.com/niyue)                           | [@FuPeiJiang](https://github.com/FuPeiJiang)                 | [@MaxPowerWasTaken](https://github.com/MaxPowerWasTaken) |
| [@emmanuel-ferdman](https://github.com/emmanuel-ferdman)   | [@fzowl](https://github.com/fzowl)                           | [@fzliu](https://github.com/fzliu)                           | [@umuthopeyildirim](https://github.com/umuthopeyildirim) |
| [@stevensu1977](https://github.com/stevensu1977)           | [@gagan-bhullar-tech](https://github.com/gagan-bhullar-tech) | [@kursataktas](https://github.com/kursataktas)               | [@erikml-db](https://github.com/erikml-db)               |
| [@alexwilcoxson-rel](https://github.com/alexwilcoxson-rel) | [@o-alexandrov](https://github.com/o-alexandrov)             | [@do-me](https://github.com/do-me)                           | [@rithikJha](https://github.com/rithikJha)               |
| [@jameswu1991](https://github.com/jameswu1991)             | [@akashsara](https://github.com/akashsara)                   | [@sayandipdutta](https://github.com/sayandipdutta)           | [@rjrobben](https://github.com/rjrobben)                 |
| [@PrashantDixit0](https://github.com/PrashantDixit0)       | [@ankitvij-db](https://github.com/ankitvij-db)               | [@jiachengdb](https://github.com/jiachengdb)                 | [@dentiny](https://github.com/dentiny)                   |
| [@tonyf](https://github.com/tonyf)                         | [@mattbasta](https://github.com/mattbasta)                   | [@bllchmbrs](https://github.com/bllchmbrs)                   | [@antoniomdk](https://github.com/antoniomdk)             |
| [@ousiax](https://github.com/ousiax)                       | [@rahuljo](https://github.com/rahuljo)                       | [@philz](https://github.com/philz)                           | [@wilhelmjung](https://github.com/wilhelmjung)           |
| [@h0rv](https://github.com/h0rv)                           | [@dsgibbons](https://github.com/dsgibbons)                   | [@maxburke](https://github.com/maxburke)                     | [@broccoliSpicy](https://github.com/broccoliSpicy)       |
| [@BitPhinix](https://github.com/BitPhinix)                 | [@inn-0](https://github.com/inn-0)                           | [@MagnusS0](https://github.com/MagnusS0)                     | [@nuvic](https://github.com/nuvic)                       |
| [@JoanFM](https://github.com/JoanFM)                       | [@thomasjpfan](https://github.com/thomasjpfan)               | [@sidharthrajaram](https://github.com/sidharthrajaram)       | [@forrestmckee](https://github.com/forrestmckee)         |
| [@NickDarvey](https://github.com/NickDarvey)               | [@heiher](https://github.com/heiher)                         | [@joshua-auchincloss](https://github.com/joshua-auchincloss) | [@josca42](https://github.com/josca42)                   |
| [@beinan](https://github.com/beinan)                       | [@harsha-mangena](https://github.com/harsha-mangena)         | [@paulwalsh-sonrai](https://github.com/paulwalsh-sonrai)     | [@paulrinaldi](https://github.com/paulrinaldi)           |
| [@gsilvestrin](https://github.com/gsilvestrin)             | [@vipul-maheshwari](https://github.com/vipul-maheshwari)     | [@ascillitoe](https://github.com/ascillitoe)                 | [@lyang24](https://github.com/lyang24)                   |
| [@vjc578db](https://github.com/vjc578db)                   | [@andrew-pienso](https://github.com/andrew-pienso)           | [@vaifai](https://github.com/vaifai)                         | [@jeff1010322](https://github.com/jeff1010322)           |
| [@fecet](https://github.com/fecet)                         | [@andrijazz](https://github.com/andrijazz)                   | [@kemingy](https://github.com/kemingy)                       | [@ahaapple](https://github.com/ahaapple)                 |
| [@jgugglberger](https://github.com/jgugglberger)           | [@bclavie](https://github.com/bclavie)                       | [@Akagi201](https://github.com/Akagi201)                     | [@schorfma](https://github.com/schorfma)                 |
| [@samuelcolvin](https://github.com/samuelcolvin)           | [@msu-reevo](https://github.com/msu-reevo)                   | [@alex766](https://github.com/alex766)                       | [@TD-Sky](https://github.com/TD-Sky)                     |
| [@timsaucer](https://github.com/timsaucer)                 | [@triandco](https://github.com/triandco)                     | [@HubertY](https://github.com/HubertY)                       | [@luohao](https://github.com/luohao)                     |
| [@pmeier](https://github.com/pmeier)                       | [@PhorstenkampFuzzy](https://github.com/PhorstenkampFuzzy)   | [@aaazzam](https://github.com/aaazzam)                       | [@guspan-tanadi](https://github.com/guspan-tanadi)       |
| [@enoonan](https://github.com/enoonan)                     |
