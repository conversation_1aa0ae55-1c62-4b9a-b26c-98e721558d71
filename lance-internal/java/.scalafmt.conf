version = 3.7.5
runner.dialect=scala212
project.git=true

align.preset = none
align.openParenDefnSite = false
align.openParenCallSite = false
align.stripMargin = true
align.tokens = []
assumeStandardLibraryStripMargin = true
danglingParentheses.preset = false
docstrings.style = Asterisk
docstrings.wrap = no
importSelectors = singleLine
indent.extendSite = 2
literals.hexDigits = Upper
maxColumn = 100
newlines.source = keep
newlines.topLevelStatementBlankLines = []
optIn.configStyleArguments = false
rewrite.imports.groups = [
  ["com\\.lancedb\\.lance\\..*"],
  ["(?!com\\.lancedb\\.lance\\.).*"],
  ["javax?\\..*"],
  ["scala\\..*"],
]
rewrite.imports.sort = scalastyle
rewrite.rules = [Imports, SortModifiers]
