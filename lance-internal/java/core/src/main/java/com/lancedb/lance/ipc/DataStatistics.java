/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.lancedb.lance.ipc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class DataStatistics implements Serializable {
  private final List<FieldStatistics> fields;

  public DataStatistics() {
    this.fields = new ArrayList<>();
  }

  // used for rust to add field statistics
  public void addFiledStatistics(FieldStatistics fieldStatistics) {
    fields.add(fieldStatistics);
  }

  public List<FieldStatistics> getFields() {
    return fields;
  }

  // get total data size of the whole dataset in bytes
  public long getDataSize() {
    return fields.stream().mapToLong(FieldStatistics::getDataSize).sum();
  }

  @Override
  public String toString() {
    return "DataStatistics{" + "fields=" + fields + '}';
  }
}
