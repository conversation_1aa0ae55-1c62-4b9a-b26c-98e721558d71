[package]
name = "lance-jni"
version = "0.31.2"
edition = "2021"
authors = ["<PERSON> Devs <<EMAIL>>"]
rust-version = "1.80"
license = "Apache-2.0"
repository = "https://github.com/lancedb/lance"
readme = "../../README.md"
description = "JNI bindings for Lance Columnar format"

[lib]
crate-type = ["cdylib"]

[dependencies]
lance = { path = "../../../rust/lance", features = ["substrait"] }
lance-datafusion = { path = "../../../rust/lance-datafusion" }
lance-encoding = { path = "../../../rust/lance-encoding" }
lance-linalg = { path = "../../../rust/lance-linalg" }
lance-index = { path = "../../../rust/lance-index" }
lance-io = { path = "../../../rust/lance-io" }
lance-core = { path = "../../../rust/lance-core" }
lance-file = { path = "../../../rust/lance-file" }
arrow = { version = "55.1", features = ["ffi"] }
arrow-schema = "55.1"
object_store = { version = "0.12.2" }
tokio = { version = "1.23", features = [
    "rt-multi-thread",
    "macros",
    "fs",
    "sync",
] }
jni = "0.21.1"
serde_json = { version = "1" }
log = "0.4"
env_logger = "0.11.7"
