<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.lancedb</groupId>
    <artifactId>lance-parent</artifactId>
    <version>0.32.0</version>
    <packaging>pom</packaging>

    <name>Lance Parent</name>
    <description>Lance Format Java API</description>
    <url>http://lancedb.com/</url>

    <developers>
        <developer>
            <name>Lance DB Dev Group</name>
            <email><EMAIL></email>
        </developer>
    </developers>
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <arrow.version>15.0.0</arrow.version>
        <substrait.version>0.28.1</substrait.version>
        <spotless.skip>false</spotless.skip>
        <spotless.version>2.30.0</spotless.version>
        <spotless.java.googlejavaformat.version>1.7</spotless.java.googlejavaformat.version>
        <scala.version>2.12.19</scala.version>
        <scala.binary.version>2.12</scala.binary.version>
        <!-- Please also update .scalafmt.conf when you change it here -->
        <spotless.scala.scalafmt.version>3.7.5</spotless.scala.scalafmt.version>
        <spotless.delimiter>package</spotless.delimiter>
        <spotless.license.header>
        /*
        * Licensed under the Apache License, Version 2.0 (the "License");
        * you may not use this file except in compliance with the License.
        * You may obtain a copy of the License at
        *
        *     http://www.apache.org/licenses/LICENSE-2.0
        *
        * Unless required by applicable law or agreed to in writing, software
        * distributed under the License is distributed on an "AS IS" BASIS,
        * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
        * See the License for the specific language governing permissions and
        * limitations under the License.
        */
        </spotless.license.header>
    </properties>

    <modules>
        <module>core</module>
    </modules>

    <scm>
        <connection>scm:git:https://github.com/lancedb/lance.git</connection>
        <developerConnection>scm:git:ssh://**************/lancedb/lance.git</developerConnection>
        <url>https://github.com/lancedb/lance</url>
    </scm>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.arrow</groupId>
                <artifactId>arrow-vector</artifactId>
                <version>${arrow.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.arrow</groupId>
                <artifactId>arrow-memory-netty</artifactId>
                <version>${arrow.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.arrow</groupId>
                <artifactId>arrow-c-data</artifactId>
                <version>${arrow.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.arrow</groupId>
                <artifactId>arrow-dataset</artifactId>
                <version>${arrow.version}</version>
            </dependency>
            <dependency>
                <groupId>org.questdb</groupId>
                <artifactId>jar-jni</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>5.10.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.14.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://s01.oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <url>https://s01.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9.1</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <configLocation>dev/checkstyle.xml</configLocation>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>true</failsOnError>
                    <violationSeverity>warning</violationSeverity>
                    <linkXRef>false</linkXRef>
                </configuration>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <compilerArgs>
                            <arg>-h</arg>
                            <arg>target/headers</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>2.5.2</version>
                </plugin>
                <plugin>
                    <groupId>com.diffplug.spotless</groupId>
                    <artifactId>spotless-maven-plugin</artifactId>
                    <version>${spotless.version}</version>
                    <configuration>
                        <skip>${spotless.skip}</skip>
                        <upToDateChecking>
                            <enabled>true</enabled>
                        </upToDateChecking>
                        <java>
                            <includes>
                                <include>src/main/java/**/*.java</include>
                                <include>src/test/java/**/*.java</include>
                            </includes>
                            <googleJavaFormat>
                                <version>${spotless.java.googlejavaformat.version}</version>
                                <style>GOOGLE</style>
                            </googleJavaFormat>

                            <importOrder>
                                <order>com.lancedb.lance,,javax,java,\#</order>
                            </importOrder>

                            <removeUnusedImports />
                        </java>
                        <scala>
                            <includes>
                                <include>src/main/scala/**/*.scala</include>
                                <include>src/main/scala-*/**/*.scala</include>
                                <include>src/test/scala/**/*.scala</include>
                                <include>src/test/scala-*/**/*.scala</include>
                            </includes>
                            <scalafmt>
                                <version>${spotless.scala.scalafmt.version}</version>
                                <scalaMajorVersion>${scala.binary.version}</scalaMajorVersion>
                                <file>${maven.multiModuleProjectDirectory}/.scalafmt.conf</file>
                            </scalafmt>
                        </scala>
                        <licenseHeader>
                            <content>${spotless.license.header}</content>
                            <delimiter>${spotless.delimiter}</delimiter>
                        </licenseHeader>
                    </configuration>
                    <executions>
                        <execution>
                            <id>spotless-check</id>
                            <phase>validate</phase>
                            <goals>
                                <goal>apply</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>jdk8</id>
            <activation>
                <jdk>[1.8,1.8.999]</jdk>
            </activation>
            <properties>
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
            </properties>
        </profile>
        <profile>
            <id>jdk11+</id>
            <activation>
                <jdk>[11,)</jdk>
            </activation>
            <properties>
                <!-- Ping release target to JDK8 to link only against Java 8 APIs -->
                <maven.compiler.release>8</maven.compiler.release>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>3.2.5</version>
                        <configuration>
                            <argLine>
                                -XX:+IgnoreUnrecognizedVMOptions
                                --add-opens=java.base/java.lang=ALL-UNNAMED
                                --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
                                --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
                                --add-opens=java.base/java.io=ALL-UNNAMED
                                --add-opens=java.base/java.net=ALL-UNNAMED
                                --add-opens=java.base/java.nio=ALL-UNNAMED
                                --add-opens=java.base/java.util=ALL-UNNAMED
                                --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
                                --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED
                                --add-opens=java.base/jdk.internal.ref=ALL-UNNAMED
                                --add-opens=java.base/sun.nio.ch=ALL-UNNAMED
                                --add-opens=java.base/sun.nio.cs=ALL-UNNAMED
                                --add-opens=java.base/sun.security.action=ALL-UNNAMED
                                --add-opens=java.base/sun.util.calendar=ALL-UNNAMED
                                --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED
                                -Djdk.reflect.useDirectMethodHandle=false
                                -Dio.netty.tryReflectionSetAccessible=true
                            </argLine>
                            <forkNode
                                implementation="org.apache.maven.plugin.surefire.extensions.SurefireForkNodeFactory" />
                            <useSystemClassLoader>false</useSystemClassLoader>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>deploy-to-ossrh</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonatype.central</groupId>
                        <artifactId>central-publishing-maven-plugin</artifactId>
                        <version>0.4.0</version>
                        <extensions>true</extensions>
                        <configuration>
                            <publishingServerId>ossrh</publishingServerId>
                            <tokenAuth>true</tokenAuth>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <version>1.6.13</version>
                        <extensions>true</extensions>
                        <configuration>
                            <serverId>ossrh</serverId>
                            <nexusUrl>https://s01.oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>true</autoReleaseAfterClose>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.5</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
