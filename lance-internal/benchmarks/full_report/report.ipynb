{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d71bc8d8-8acb-4f71-a2c0-c1bc965e34a1", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.dont_write_bytecode = True\n", "\n", "import os\n", "\n", "module_path = os.path.abspath(os.path.join('.'))\n", "if module_path not in sys.path:\n", "    sys.path.append(module_path)"]}, {"cell_type": "code", "execution_count": 2, "id": "2d95e5a1-fcec-4566-96d9-2e717d53414e", "metadata": {}, "outputs": [], "source": ["from _lib import knn, write_lance, _get_nyt_vectors\n", "\n", "import numpy as np\n", "import tempfile\n", "import random\n", "import lance\n", "import pandas as pd\n", "import seaborn as sns\n", "\n", "from matplotlib import pyplot as plt\n", "from tqdm.auto import tqdm"]}, {"cell_type": "code", "execution_count": 15, "id": "9fe600ec-7018-4605-a860-d18042d4eb5f", "metadata": {}, "outputs": [], "source": ["def run_test(\n", "    data,\n", "    query,\n", "    metric,\n", "    num_partitions=256,\n", "    num_sub_vectors=8,\n", "    nprobes_list=[1, 2, 5, 10, 16],\n", "    refine_factor_list=[1, 2, 5, 10, 20],\n", "):\n", "    in_sample = data[random.sample(range(data.shape[0]), 1000), :]\n", "    # ground truth\n", "    print(\"generating gt\")\n", "\n", "    gt = knn(query, data, metric, 10)\n", "    gt_in_sample = knn(in_sample, data, metric, 10)\n", "\n", "    print(\"generated gt\")\n", "    \n", "    with tempfile.TemporaryDirectory() as d:\n", "        write_lance(d, data)\n", "        ds = lance.dataset(d)\n", "\n", "        for q, target in zip(tqdm(in_sample, desc=\"checking brute force\"), gt_in_sample):\n", "            res = ds.to_table(nearest={\n", "                \"column\": \"vec\",\n", "                \"q\": q,\n", "                \"k\": 10,\n", "                \"metric\": metric,\n", "            }, columns=[\"id\"])\n", "            assert len(np.intersect1d(res[\"id\"].to_numpy(), target)) == 10\n", "    \n", "        ds = ds.create_index(\"vec\", \"IVF_PQ\", metric=metric, num_partitions=num_partitions, num_sub_vectors=num_sub_vectors)\n", "    \n", "        recall_data = []\n", "        for nprobes in nprobes_list:\n", "            for refine_factor in refine_factor_list:\n", "                hits = 0\n", "                # check that brute force impl is correct\n", "                for q, target in zip(tqdm(query, desc=f\"out of sample, nprobes={nprobes}, refine={refine_factor}\"), gt):\n", "                    res = ds.to_table(nearest={\n", "                        \"column\": \"vec\",\n", "                        \"q\": q,\n", "                        \"k\": 10,\n", "                        \"nprobes\": nprobes,\n", "                        \"refine_factor\": refine_factor,\n", "                    }, columns=[\"id\"])[\"id\"].to_numpy()\n", "                    hits += len(np.intersect1d(res, target))\n", "                recall_data.append([\n", "                    \"out_of_sample\",\n", "                    nprobes,\n", "                    refine_factor,\n", "                    hits / 10 / len(gt),\n", "                ])\n", "                # check that brute force impl is correct\n", "                for q, target in zip(tqdm(in_sample, desc=f\"in sample nprobes={nprobes}, refine={refine_factor}\"), gt_in_sample):\n", "                    res = ds.to_table(nearest={\n", "                        \"column\": \"vec\",\n", "                        \"q\": q,\n", "                        \"k\": 10,\n", "                        \"nprobes\": nprobes,\n", "                        \"refine_factor\": refine_factor,\n", "                    }, columns=[\"id\"])[\"id\"].to_numpy()\n", "                    hits += len(np.intersect1d(res, target))\n", "                recall_data.append([\n", "                    \"in_sample\",\n", "                    nprobes,\n", "                    refine_factor,\n", "                    hits / 10 / len(gt_in_sample),\n", "                ])\n", "    return recall_data"]}, {"cell_type": "code", "execution_count": 16, "id": "a68e21fb-7d4a-49af-9242-cd052874a98a", "metadata": {}, "outputs": [], "source": ["def make_plot(recall_data):\n", "    df = pd.DataFrame(recall_data, columns=[\"case\", \"nprobes\", \"refine_factor\", \"recall\"])\n", "    \n", "    num_cases = len(df[\"case\"].unique())\n", "    (fig, axs) = plt.subplots(1, 2, figsize=(16, 8))\n", "    \n", "    for case, ax in zip(df[\"case\"].unique(), axs):\n", "        current_case = df[df[\"case\"] == case]\n", "        sns.heatmap(\n", "            current_case.drop(columns=[\"case\"]).set_index([\"nprobes\", \"refine_factor\"])[\"recall\"].unstack(),\n", "            annot=True,\n", "            ax=ax,\n", "        ).set(title=f\"Recall -- {case}\")"]}, {"cell_type": "code", "execution_count": 19, "id": "9eee7907-3494-47a5-ac8a-a4bff2ab7055", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# test randomly generated data\n", "data = np.random.standard_normal((100000, 64))\n", "query = np.random.standard_normal((1000, 64))\n", "\n", "recall_data = run_test(\n", "    data,\n", "    query,\n", "    \"L2\",\n", ")\n", "\n", "make_plot(recall_data)"]}, {"cell_type": "code", "execution_count": 22, "id": "0bf731d5-7a4d-46d4-8405-4abadb5d754d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["generating gt\n", "generated gt\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fcdd5f7276cc4bf29f7b81a0d0de895b", "version_major": 2, "version_minor": 0}, "text/plain": ["checking brute force:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["[2024-03-01T06:14:10Z WARN  lance_linalg::kmeans] KMeans: more than 10% of clusters are empty: 221 of 256.\n", "    Help: this could mean your dataset is too small to have a meaningful index (less than 5000 vectors) or has many duplicate vectors.\n", "[2024-03-01T06:14:10Z WARN  lance_linalg::kmeans] KMeans: more than 10% of clusters are empty: 197 of 256.\n", "    Help: this could mean your dataset is too small to have a meaningful index (less than 5000 vectors) or has many duplicate vectors.\n", "[2024-03-01T06:14:10Z WARN  lance_linalg::kmeans] KMeans: more than 10% of clusters are empty: 104 of 256.\n", "    Help: this could mean your dataset is too small to have a meaningful index (less than 5000 vectors) or has many duplicate vectors.\n", "[2024-03-01T06:14:10Z WARN  lance_linalg::kmeans] KMeans: more than 10% of clusters are empty: 29 of 256.\n", "    Help: this could mean your dataset is too small to have a meaningful index (less than 5000 vectors) or has many duplicate vectors.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c6406ecb22a84340908f3f4bdd201bdf", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fc4d399fa993434bba94a07ae16d930d", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0336a86222f049b0bafb1c233270e7ba", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2bd45486d46347b7a19ab7b5b1c234d0", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dc1f8f32d3a64ed5981e6c02f695b6e7", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "aa4ec62435274d278ae3c3624311b5d2", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f294dfc4126542c789adb06f3bdd71bb", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "398b9b677caf44778f25952c228f4435", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "524783c9747e4ed7b948e8b515c1e109", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "afd0a179b02848ada38d8445e83a2c47", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4d713120c5ec4065bd0c49ddd452bd5f", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "20a7da8e2f5d4976b1348b2419428854", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f207976dad924a95a0c9b49b719d2c89", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "398aba52197a48d1a1ebf04c5957198c", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a2a861c99ff149aab56ed38f95867a9c", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "98720d7a7dfe49928150c37449c67f7e", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "af2667b5cd4f494ea1a4cbfcf6d4d23a", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b01cf7073a9a462b840bd7614a951acf", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f5982bf698c841e4b03c332c96595325", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5931b46eed7142eb9961d58c39319638", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bfe4356aedc94afabe9fd96aa21d38df", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e85d8e6206f64191beae694aa99ae0f3", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e153cd4b26744d4a8b426e8258de821d", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b4b7015600594985acea1e9049d7aead", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c1cc432be72040bfba1512ef15b977ff", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "13f7e55bb006462d92b737362f8bb1c8", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2acabe91cdee46ccbdc50e182d3738d2", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "04d6fa93645f468f8b6282857708d6d4", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "be4b01bfa3a74323a8c908a9acaffbf5", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3303e0536ec548249f504bb3b0c9b641", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e003f7b5b6e34584936fbab6ba0a1bbc", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5fb7060953974ae283f19695b977a369", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c6b697c6d3264835a379c65efbc3dd32", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8e1dce7456824da3bb20f4a2fdbc7c71", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "419619adcc8e4d6e904d5be6c04a44a1", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "21daf925710d4d7f81091444205f9981", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "57674bd73bd744d1a376915008b2b2e2", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6e8f4379268340df89d157ab65c0b4f0", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ca1c000cb6b04dfc8b5cccc4bf9fcc9b", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e30f4adf4ff74a3788ad425f9c1e058f", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6f423f2235874cac85efb3da6f7f567c", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "456c896d0d054645b0a63778dee2fc1a", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "35874591d3c14aba9ea0051064c48f33", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6d333c9bb7a34472881e4367806b9600", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "db9e427d4b7b4d24a1d724e286245ae3", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fb360097a4964e9f8ea97e4d73f9f413", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e355c060855e4c29852c6409e882fed3", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a090a522b04442119f8c147465a0231f", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d4ec053e1b124f3b861227f74c3740cd", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7182fe1a09e34da49b0655c9d08bf7ba", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# test randomly generated data -- cosine\n", "data = np.random.standard_normal((100000, 64))\n", "query = np.random.standard_normal((1000, 64))\n", "\n", "recall_data = run_test(\n", "    data,\n", "    query,\n", "    \"cosine\",\n", ")\n", "\n", "make_plot(recall_data)"]}, {"cell_type": "code", "execution_count": 22, "id": "f8243a51-5222-4446-9696-a031281f17a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loading from cache\n", "generating gt\n", "generated gt\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6afedef81f134cefac769b2c2afc5e96", "version_major": 2, "version_minor": 0}, "text/plain": ["checking brute force:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "924edcc8518b45259934e74b867b8563", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=1:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "97cd2aa87d974a02a4695c726068830a", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "70b6819d2ecb48caa5e78fa79eef1afa", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=2:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "496f78313d8448c796ccce13f4ccf019", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b8bfeb500f3846379e37025263ac528e", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=5:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4abc75110e3449e382e0c6e6fe1f92e0", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "709f036a4e544323b5ade1649d2ca80d", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=10:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "48e94c93e0564671978785e102db8a08", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dc353c63cbb644f2be9e8168d0bf7615", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=20:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e55ca9b3f3c945f2a21329d71eeb9f5c", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d736399c259e48bc8783fcfb5a445f19", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=1:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "91f144fbcdcf43a6a50d7b21c30c0781", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5d2d5d2244b94a958383f2d4c7f69255", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=2:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bc43362dbe29422a9445e6d42dff3848", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ae23f5985b3046d1858e8174eb24541d", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=5:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "69add85ecaca47b9a5797383628c2b1d", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3b28b14efc8342a5b098172db49dec46", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=10:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e89aeca09e6348e6b2886dd724b82d36", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "168521e5812d4660a869d0f3a00843d3", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=20:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3c8b7558bfe248a68f9d214a4f2578d4", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "90402288247646c5a7c9ea2627696189", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=1:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f053e9b5f67045b89445400804fa0f0e", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "43aea415aed846ce953a53c3d4941338", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=2:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f1fd86121c894470b6ad64c650485811", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "912a045b19cf403c973f6b3018310d63", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=5:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "be1096438c224fed853cfd3bae0cd6d2", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6b424ba89ecd44d39aa6c41f740ce369", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=10:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8c196e40a694462381a00112731a7b58", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "14ba57fc240b4613b94d073de0fbc2c0", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=20:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "834530eecf864799bc3ebda8401efd16", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "21666b3d9a414b58814262b6c2cecc07", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=1:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c83eb24404a848e0a8685cbdc478e411", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4386c33470974b4a90c66c6712096d4d", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=2:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f90b324ab93642a88fbaf24b51e069e7", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e4a916edfccd43f897ddf222382385f2", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=5:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "62822ec4bab54e1f92b286fc450d40ae", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e77d1ceb8e3c4cf4af7b83c070811af6", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=10:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "14b4b0a77f2e48c7b9656d54819a1074", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9d993ddaf7bd4c6eaa5e51f95a0dd40a", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=20:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "efacaa6f9c1e4ff9bc32b73ed2b53b2b", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "029888e8666a44328f4e3ca3c6341ae9", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=1:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "67afdef20963464c9ff552b06e3701ce", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "78c5ef407d284f438efb861479311052", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=2:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bb63726d4edc4247a4ebfcdda3f71863", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fca4921320c449049ea21ce5ed8ec7da", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=5:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "65607ddb49164019b10b743cc83cb9b0", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "624ed6ae6a0f4e118821f4e6c4e66278", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=10:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1bb79be7a1ef45ee959618f5ba7e9364", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "28d4724ceb01431787242e95dadd2fe1", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=20:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eb75991b1c764c53b1ae73344fb36d86", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# test NYT -- TF-IDF sparse vectors projected on to 256D dense -- cosine\n", "data = _get_nyt_vectors()\n", "data = data[np.linalg.norm(data, axis=1) != 0]\n", "data = np.unique(data, axis=0)\n", "query = np.random.standard_normal((100, 256))\n", "\n", "recall_data = run_test(\n", "    data,\n", "    query,\n", "    \"cosine\",\n", "    num_partitions=256,\n", "    num_sub_vectors=32,\n", ")\n", "\n", "make_plot(recall_data)"]}, {"cell_type": "code", "execution_count": 19, "id": "8c5fbd36-6f14-4ae4-adcf-97b103331c90", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loading from cache\n", "generating gt\n", "generated gt\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dfc2ca7d16704ff99a653810421a6ade", "version_major": 2, "version_minor": 0}, "text/plain": ["checking brute force:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c52e2091dfb94076be940feba1d90bb6", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=1:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "622af34110514fa4a6cb2151081fe862", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5897d44f59d8417a9ed99616fd4310ae", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=2:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ac20497304ad44eea0dbf19575da5f6b", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "245672d757dd4768a69a0c72abc7b134", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=5:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4151372561474e5c9b2c0bd3cc6e343e", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c3546d78a1c34539b5ebdf5bf5da1c7a", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=10:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "afd094b975c94f4f9258e908ead6c319", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "49a1cb146ec94a6bb1391aeef3994cbd", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=1, refine=20:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cef8a37d7b194a4cb99b6bb836577d57", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=1, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5d3539daa1a243318cae63461744ac4c", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=1:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c226c672f10a4c0090a86594f0524419", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8f4ac498a2cb4a499e2069101e5d7ed6", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=2:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "77337a6e3a5a45799f0923ec6e308d66", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f9b7ae6781944672b95cfa0cae57e87e", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=5:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4802fb3bc31b4a2787ba90a17a08b5a5", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "05d47bb6c3d2444588c48e8e3f924e2e", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=10:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "59086357ea9844d7979cc1b598dd0629", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a0d66c1bc509408ab998b748c6b16210", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=2, refine=20:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "be18686ec95540b38347cc8c69be05ff", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=2, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "267f00a19de149b18afcd8a84d1db00d", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=1:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f22d583cc4734d179f9c85a494ff45ca", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fccc4916f54243cb84608d974a22480c", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=2:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6b970f4befbc421f876a67591f2e2539", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "584f55a2862e4e24a52fb990ba2df322", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=5:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "63a5af7145324658982dddf0386c6ace", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "119ad3c475b24914b4bcc85bd1de85b5", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=10:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d53636167837409baa7b4f3a60fbf137", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "04b93e0f5f5045d48aab165554da1def", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=5, refine=20:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b029ce69276c493cbf9d983be8e08acf", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=5, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7e1e1cf9e6974c6f8723e9bf15ab1c2c", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=1:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9f6a9ffcedfc4daabe0fefb542da9f59", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1afe5c03c3e24072ab1a83478f5311d9", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=2:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "92bc3fcc262f45f6806c2a59ea713c89", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "003c9dbd0a9d461da3cbcc360ebcb433", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=5:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c6c291f435a444b19a4c429785581331", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d036644c93574c5781cb68487a27b9aa", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=10:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4770f7c60a1141ccbbd6a47d9492e829", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d7d3c8ed7f4c4b328ef1ed6fb101fa96", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=10, refine=20:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "36ca7701a389444dbe7c0d3c7ad09d1c", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=10, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6cf75c73abf94da989e8ffe722af72e8", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=1:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "554cc401c15043c9b6a3cdfbcbb25f11", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=1:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "33192bd5e36c492f9c4553a4a5fdca1f", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=2:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1961f1fad3cc44318e9af4a237acc3b4", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=2:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2cceda24b99941ec9ce41fa0f38fea8d", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=5:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "19ad7ed517e64e298907c99ff12f0925", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=5:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a19d9c4b8d8d4fa4bff1bdedf2edea22", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=10:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c983aea660ef45bb8010840f28c71cef", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=10:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fad3a14be94843fcadd24a1895fe6435", "version_major": 2, "version_minor": 0}, "text/plain": ["out of sample, nprobes=16, refine=20:   0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "19b1bae8f0fe469783b9b725e07c6476", "version_major": 2, "version_minor": 0}, "text/plain": ["in sample nprobes=16, refine=20:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# test NYT -- TF-IDF sparse vectors projected on to 256D dense -- normalized L2\n", "data = _get_nyt_vectors()\n", "data = data[np.linalg.norm(data, axis=1) != 0]\n", "data = np.unique(data, axis=0)\n", "data /= np.linalg.norm(data, axis=1)[:, None]\n", "\n", "# use the same out of sample query\n", "\n", "\n", "recall_data = run_test(\n", "    data,\n", "    query,\n", "    \"L2\",\n", "    num_partitions=512,\n", "    num_sub_vectors=32,\n", ")\n", "\n", "make_plot(recall_data)"]}, {"cell_type": "code", "execution_count": null, "id": "45249d41-24c5-4845-8313-942d4d9853c2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}