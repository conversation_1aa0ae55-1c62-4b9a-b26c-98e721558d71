{"cells": [{"cell_type": "markdown", "id": "7f39e160", "metadata": {}, "source": ["# Analysis\n", "\n", "M2 Macbook Air (2023)\n", "Memory: 24GB\n", "Disk: 512GB\n", "\n", "Vectors: 1M x 768D  (randomly generated)\n", "\n", "Write parameters:\n", "- `max_rows_per_group`: 8192\n", "- `max_rows_per_file`: 1024 * 1024 (default)\n", "\n", "Test configurations:\n", "- `IVF`: [32, 64, 256, 1024]\n", "- `PQ`: [16,32,64,96]\n", "- `nprobes`: [1,10,25,50]\n", "- `refine_factor`: [None, 1, 5, 10]\n", "\n", "Code:\n", "Call `run_tests` in [perf.py](perf.py)"]}, {"cell_type": "code", "execution_count": 1, "id": "b146aa4a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.read_csv(\"query.csv\")"]}, {"cell_type": "code", "execution_count": 5, "id": "b9592a11", "metadata": {}, "outputs": [], "source": ["import seaborn as sns\n", "sns.set_style(\"darkgrid\")"]}, {"cell_type": "markdown", "id": "d228ae85", "metadata": {}, "source": ["# Histogram of median query latency"]}, {"cell_type": "code", "execution_count": 8, "id": "1d0bbfa4", "metadata": {}, "outputs": [{"data": {"text/plain": ["<seaborn.axisgrid.FacetGrid at 0x16ae447c0>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ax = sns.displot(df, x=\"50%\")\n", "ax.set(xlabel=\"Median response time seconds\", ylabel=\"Number of configurations\")"]}, {"cell_type": "code", "execution_count": 13, "id": "d4aeb30f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ivf</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>32.0</th>\n", "      <td>64.0</td>\n", "      <td>0.032082</td>\n", "      <td>0.026801</td>\n", "      <td>0.001920</td>\n", "      <td>0.010757</td>\n", "      <td>0.024426</td>\n", "      <td>0.051816</td>\n", "      <td>0.085572</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64.0</th>\n", "      <td>64.0</td>\n", "      <td>0.026413</td>\n", "      <td>0.024246</td>\n", "      <td>0.001613</td>\n", "      <td>0.005936</td>\n", "      <td>0.021828</td>\n", "      <td>0.036099</td>\n", "      <td>0.087138</td>\n", "    </tr>\n", "    <tr>\n", "      <th>256.0</th>\n", "      <td>64.0</td>\n", "      <td>0.010854</td>\n", "      <td>0.009261</td>\n", "      <td>0.001675</td>\n", "      <td>0.003519</td>\n", "      <td>0.008136</td>\n", "      <td>0.014876</td>\n", "      <td>0.035234</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1024.0</th>\n", "      <td>64.0</td>\n", "      <td>0.007695</td>\n", "      <td>0.003927</td>\n", "      <td>0.003198</td>\n", "      <td>0.004661</td>\n", "      <td>0.006239</td>\n", "      <td>0.009814</td>\n", "      <td>0.018332</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        count      mean       std       min       25%       50%       75%  \\\n", "ivf                                                                         \n", "32.0     64.0  0.032082  0.026801  0.001920  0.010757  0.024426  0.051816   \n", "64.0     64.0  0.026413  0.024246  0.001613  0.005936  0.021828  0.036099   \n", "256.0    64.0  0.010854  0.009261  0.001675  0.003519  0.008136  0.014876   \n", "1024.0   64.0  0.007695  0.003927  0.003198  0.004661  0.006239  0.009814   \n", "\n", "             max  \n", "ivf               \n", "32.0    0.085572  \n", "64.0    0.087138  \n", "256.0   0.035234  \n", "1024.0  0.018332  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby(\"ivf\")[\"50%\"].describe()"]}, {"cell_type": "code", "execution_count": 15, "id": "97cce63e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "    <tr>\n", "      <th>pq</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>16.0</th>\n", "      <td>64.0</td>\n", "      <td>0.009402</td>\n", "      <td>0.006587</td>\n", "      <td>0.001613</td>\n", "      <td>0.003900</td>\n", "      <td>0.007260</td>\n", "      <td>0.014019</td>\n", "      <td>0.022231</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32.0</th>\n", "      <td>64.0</td>\n", "      <td>0.013085</td>\n", "      <td>0.010227</td>\n", "      <td>0.001682</td>\n", "      <td>0.004509</td>\n", "      <td>0.009857</td>\n", "      <td>0.019022</td>\n", "      <td>0.032448</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64.0</th>\n", "      <td>64.0</td>\n", "      <td>0.023195</td>\n", "      <td>0.021240</td>\n", "      <td>0.001956</td>\n", "      <td>0.005341</td>\n", "      <td>0.014339</td>\n", "      <td>0.036617</td>\n", "      <td>0.060211</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96.0</th>\n", "      <td>64.0</td>\n", "      <td>0.031362</td>\n", "      <td>0.030544</td>\n", "      <td>0.002154</td>\n", "      <td>0.006557</td>\n", "      <td>0.018382</td>\n", "      <td>0.051712</td>\n", "      <td>0.087138</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      count      mean       std       min       25%       50%       75%  \\\n", "pq                                                                        \n", "16.0   64.0  0.009402  0.006587  0.001613  0.003900  0.007260  0.014019   \n", "32.0   64.0  0.013085  0.010227  0.001682  0.004509  0.009857  0.019022   \n", "64.0   64.0  0.023195  0.021240  0.001956  0.005341  0.014339  0.036617   \n", "96.0   64.0  0.031362  0.030544  0.002154  0.006557  0.018382  0.051712   \n", "\n", "           max  \n", "pq              \n", "16.0  0.022231  \n", "32.0  0.032448  \n", "64.0  0.060211  \n", "96.0  0.087138  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby(\"pq\")[\"50%\"].describe()"]}, {"cell_type": "code", "execution_count": 16, "id": "39e841a1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "    <tr>\n", "      <th>nprobes</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1.0</th>\n", "      <td>64.0</td>\n", "      <td>0.003308</td>\n", "      <td>0.001312</td>\n", "      <td>0.001613</td>\n", "      <td>0.002258</td>\n", "      <td>0.003223</td>\n", "      <td>0.003966</td>\n", "      <td>0.007116</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10.0</th>\n", "      <td>64.0</td>\n", "      <td>0.013890</td>\n", "      <td>0.012305</td>\n", "      <td>0.003736</td>\n", "      <td>0.005845</td>\n", "      <td>0.007927</td>\n", "      <td>0.017415</td>\n", "      <td>0.049728</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25.0</th>\n", "      <td>64.0</td>\n", "      <td>0.026239</td>\n", "      <td>0.022855</td>\n", "      <td>0.006177</td>\n", "      <td>0.008876</td>\n", "      <td>0.016646</td>\n", "      <td>0.034535</td>\n", "      <td>0.084565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50.0</th>\n", "      <td>64.0</td>\n", "      <td>0.033607</td>\n", "      <td>0.024742</td>\n", "      <td>0.008927</td>\n", "      <td>0.014828</td>\n", "      <td>0.024181</td>\n", "      <td>0.041161</td>\n", "      <td>0.087138</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         count      mean       std       min       25%       50%       75%  \\\n", "nprobes                                                                      \n", "1.0       64.0  0.003308  0.001312  0.001613  0.002258  0.003223  0.003966   \n", "10.0      64.0  0.013890  0.012305  0.003736  0.005845  0.007927  0.017415   \n", "25.0      64.0  0.026239  0.022855  0.006177  0.008876  0.016646  0.034535   \n", "50.0      64.0  0.033607  0.024742  0.008927  0.014828  0.024181  0.041161   \n", "\n", "              max  \n", "nprobes            \n", "1.0      0.007116  \n", "10.0     0.049728  \n", "25.0     0.084565  \n", "50.0     0.087138  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby(\"nprobes\")[\"50%\"].describe()"]}, {"cell_type": "code", "execution_count": 17, "id": "ae82b248", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "    <tr>\n", "      <th>refine_factor</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1.0</th>\n", "      <td>64.0</td>\n", "      <td>0.019046</td>\n", "      <td>0.021429</td>\n", "      <td>0.001613</td>\n", "      <td>0.004616</td>\n", "      <td>0.010292</td>\n", "      <td>0.024471</td>\n", "      <td>0.085852</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5.0</th>\n", "      <td>64.0</td>\n", "      <td>0.019357</td>\n", "      <td>0.021453</td>\n", "      <td>0.001978</td>\n", "      <td>0.004939</td>\n", "      <td>0.010700</td>\n", "      <td>0.024362</td>\n", "      <td>0.086231</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10.0</th>\n", "      <td>64.0</td>\n", "      <td>0.019685</td>\n", "      <td>0.021409</td>\n", "      <td>0.002313</td>\n", "      <td>0.005290</td>\n", "      <td>0.011049</td>\n", "      <td>0.025032</td>\n", "      <td>0.087138</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               count      mean       std       min       25%       50%  \\\n", "refine_factor                                                            \n", "1.0             64.0  0.019046  0.021429  0.001613  0.004616  0.010292   \n", "5.0             64.0  0.019357  0.021453  0.001978  0.004939  0.010700   \n", "10.0            64.0  0.019685  0.021409  0.002313  0.005290  0.011049   \n", "\n", "                    75%       max  \n", "refine_factor                      \n", "1.0            0.024471  0.085852  \n", "5.0            0.024362  0.086231  \n", "10.0           0.025032  0.087138  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby(\"refine_factor\")[\"50%\"].describe()"]}, {"cell_type": "code", "execution_count": 18, "id": "314fb241", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='nprobes', ylabel='50%'>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.scatterplot(data=df, x=\"nprobes\", y=\"50%\", hue=\"ivf\", style=\"pq\")"]}, {"cell_type": "code", "execution_count": null, "id": "ffec06bd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}