#!/usr/bin/env bash
#
# Copyright 2023 Lance Developers
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

wget ftp://ftp.irisa.fr/local/texmex/corpus/sift.tar.gz
tar -xzf sift.tar.gz
rm -rf sift1m_*.lance
./datagen.py sift/sift_base.fvecs sift1m.lance
./index.py sift1m.lance -i 256 -p 16 -c vector
./metrics.py sift1m.lance benchmark.csv -i 256
