// SPDX-License-Identifier: Apache-2.0
// SPDX-FileCopyrightText: Copyright The Lance Authors

//! Routines for compressing and decompressing constant-encoded data

use crate::{
    buffer::<PERSON><PERSON>uffer,
    compression::BlockDecompressor,
    data::{ConstantDataBlock, DataBlock},
};

use lance_core::Result;

/// A decompressor for constant-encoded data
#[derive(Debug)]
pub struct ConstantDecompressor {
    scalar: <PERSON><PERSON>uffer,
}

impl ConstantDecompressor {
    pub fn new(scalar: <PERSON><PERSON><PERSON><PERSON>) -> Self {
        Self {
            scalar: scalar.into_borrowed(),
        }
    }
}

impl BlockDecompressor for ConstantDecompressor {
    fn decompress(&self, _data: <PERSON><PERSON>uffer, num_values: u64) -> Result<DataBlock> {
        Ok(DataBlock::Constant(ConstantDataBlock {
            data: self.scalar.try_clone().unwrap(),
            num_values,
        }))
    }
}
