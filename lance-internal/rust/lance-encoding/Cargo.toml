[package]
name = "lance-encoding"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
readme = "README.md"
description = "Encoders and decoders for the Lance file format"
keywords.workspace = true
categories.workspace = true
rust-version.workspace = true

[dependencies]
lance-arrow.workspace = true
lance-core.workspace = true
arrow.workspace = true
arrow-arith.workspace = true
arrow-array.workspace = true
arrow-data.workspace = true
arrow-buffer.workspace = true
arrow-cast.workspace = true
arrow-schema.workspace = true
arrow-select.workspace = true
bytes.workspace = true
futures.workspace = true
fsst.workspace = true
hex = "0.4.3"
itertools.workspace = true
log.workspace = true
num-traits.workspace = true
prost.workspace = true
hyperloglogplus.workspace = true
prost-types.workspace = true
rand.workspace = true
snafu.workspace = true
tokio.workspace = true
tracing.workspace = true
xxhash-rust = { version = "0.8.15", features = ["xxh3"] }
zstd.workspace = true
bytemuck = "1.14"
arrayref = "0.3.7"
paste = "1.0.15"
seq-macro = "0.3.5"
byteorder.workspace = true
lz4 = "1.28.1"

[dev-dependencies]
lance-testing.workspace = true
lance-datagen.workspace = true
rand.workspace = true
rstest.workspace = true
tempfile.workspace = true
test-log.workspace = true
criterion = { workspace = true }
rand_xoshiro = "0.6.0"

[build-dependencies]
prost-build.workspace = true
protobuf-src = { version = "2.1", optional = true }

[target.'cfg(target_os = "linux")'.dev-dependencies]
pprof = { workspace = true }

[features]
protoc = ["dep:protobuf-src"]

[package.metadata.docs.rs]
# docs.rs uses an older version of Ubuntu that does not have the necessary protoc version
features = ["protoc"]

[[bench]]
name = "decoder"
harness = false

[[bench]]
name = "buffer"
harness = false

[lints]
workspace = true
