[package]
name = "lance-io"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
readme = "README.md"
description = "I/O utilities for Lance"
keywords.workspace = true
categories.workspace = true
rust-version.workspace = true

[dependencies]

object_store = { workspace = true }
opendal = { workspace = true, optional = true }
object_store_opendal = { workspace = true, optional = true }
lance-arrow.workspace = true
lance-core.workspace = true
arrow = { workspace = true, features = ["ffi"] }
arrow-arith.workspace = true
arrow-array.workspace = true
arrow-buffer.workspace = true
arrow-cast.workspace = true
arrow-data.workspace = true
arrow-schema.workspace = true
arrow-select.workspace = true
async-recursion.workspace = true
async-trait.workspace = true
aws-config = { workspace = true, optional = true }
aws-credential-types = { workspace = true, optional = true }
byteorder.workspace = true
bytes.workspace = true
chrono.workspace = true
deepsize.workspace = true
futures.workspace = true
log.workspace = true
pin-project.workspace = true
prost.workspace = true
serde.workspace = true
shellexpand.workspace = true
snafu.workspace = true
tokio.workspace = true
tracing.workspace = true
url.workspace = true
path_abs.workspace = true
rand.workspace = true
async-priority-channel = "0.2.0"

[dev-dependencies]
criterion.workspace = true
tempfile.workspace = true
test-log.workspace = true
mockall.workspace = true
rstest.workspace = true

[target.'cfg(target_os = "linux")'.dev-dependencies]
pprof.workspace = true

[[bench]]
name = "scheduler"
harness = false

[features]
default = ["aws", "azure", "gcp"]
gcs-test = []
gcp = ["object_store/gcp"]
aws = ["object_store/aws", "aws-config", "aws-credential-types"]
azure = ["object_store/azure"]
oss = ["opendal/services-oss", "object_store_opendal"]

[lints]
workspace = true
