// SPDX-License-Identifier: Apache-2.0
// SPDX-FileCopyrightText: Copyright The Lance Authors

use std::sync::Arc;

use arrow_array::{
    types::{UInt32Type, UInt64Type},
    RecordBatchReader,
};
use async_trait::async_trait;
use criterion::{criterion_group, criterion_main, Criterion};
use datafusion::{physical_plan::SendableRecordBatchStream, scalar::ScalarValue};
use futures::{FutureExt, TryStreamExt};
use lance::{io::ObjectStore, Dataset};
use lance_core::{cache::LanceCache, Result};
use lance_datafusion::utils::reader_to_stream;
use lance_datagen::{array, gen, BatchCount, RowCount};
use lance_index::metrics::NoOpMetricsCollector;
use lance_index::scalar::{
    btree::{train_btree_index, BTreeIndex, TrainingSource, DEFAULT_BTREE_BATCH_SIZE},
    flat::FlatIndexMetadata,
    lance_format::LanceIndexStore,
    IndexStore, SargableQuery, ScalarIndex, SearchResult,
};
#[cfg(target_os = "linux")]
use pprof::criterion::{Output, PProfProfiler};
use tempfile::TempDir;

struct BenchmarkFixture {
    _datadir: TempDir,
    index_store: Arc<dyn IndexStore>,
    baseline_dataset: Arc<Dataset>,
}

struct BenchmarkDataSource {}

impl BenchmarkDataSource {
    fn test_data() -> impl RecordBatchReader {
        gen()
            .col("values", array::step::<UInt32Type>())
            .col("row_ids", array::step::<UInt64Type>())
            .into_reader_rows(RowCount::from(1024), BatchCount::from(100 * 1024))
    }
}

#[async_trait]
impl TrainingSource for BenchmarkDataSource {
    async fn scan_ordered_chunks(
        self: Box<Self>,
        _chunk_size: u32,
    ) -> Result<SendableRecordBatchStream> {
        Ok(reader_to_stream(Box::new(Self::test_data())))
    }

    async fn scan_unordered_chunks(
        self: Box<Self>,
        _chunk_size: u32,
    ) -> Result<SendableRecordBatchStream> {
        Ok(reader_to_stream(Box::new(Self::test_data())))
    }
}

impl BenchmarkFixture {
    fn test_store(tempdir: &TempDir) -> Arc<dyn IndexStore> {
        let test_path = tempdir.path();
        let (object_store, test_path) =
            ObjectStore::from_uri(test_path.as_os_str().to_str().unwrap())
                .now_or_never()
                .unwrap()
                .unwrap();
        Arc::new(LanceIndexStore::new(
            object_store,
            test_path,
            Arc::new(LanceCache::no_cache()),
        ))
    }

    async fn write_baseline_data(tempdir: &TempDir) -> Arc<Dataset> {
        let test_path = tempdir.path().as_os_str().to_str().unwrap();
        Arc::new(
            Dataset::write(BenchmarkDataSource::test_data(), test_path, None)
                .await
                .unwrap(),
        )
    }

    async fn train_scalar_index(index_store: &Arc<dyn IndexStore>) {
        let sub_index_trainer = FlatIndexMetadata::new(arrow_schema::DataType::UInt32);

        train_btree_index(
            Box::new(BenchmarkDataSource {}),
            &sub_index_trainer,
            index_store.as_ref(),
            DEFAULT_BTREE_BATCH_SIZE as u32,
        )
        .await
        .unwrap();
    }

    async fn open() -> Self {
        let tempdir = tempfile::tempdir().unwrap();
        let index_store = Self::test_store(&tempdir);
        let baseline_dataset = Self::write_baseline_data(&tempdir).await;
        Self::train_scalar_index(&index_store).await;

        Self {
            _datadir: tempdir,
            index_store,
            baseline_dataset,
        }
    }
}

async fn baseline_equality_search(fixture: &BenchmarkFixture) {
    let mut stream = fixture
        .baseline_dataset
        .scan()
        .filter("values == 10000")
        .unwrap()
        .with_row_id()
        .try_into_stream()
        .await
        .unwrap();
    let mut num_rows = 0;
    while let Some(batch) = stream.try_next().await.unwrap() {
        num_rows += batch.num_rows();
    }
    assert_eq!(num_rows, 1);
}

async fn warm_indexed_equality_search(index: &BTreeIndex) {
    let result = index
        .search(
            &SargableQuery::Equals(ScalarValue::UInt32(Some(10000))),
            &NoOpMetricsCollector,
        )
        .await
        .unwrap();
    let SearchResult::Exact(row_ids) = result else {
        panic!("Expected exact results")
    };
    assert_eq!(row_ids.len(), Some(1));
}

async fn baseline_inequality_search(fixture: &BenchmarkFixture) {
    let mut stream = fixture
        .baseline_dataset
        .scan()
        .filter("values >= 50000000")
        .unwrap()
        .with_row_id()
        .try_into_stream()
        .await
        .unwrap();
    let mut num_rows = 0;
    while let Some(batch) = stream.try_next().await.unwrap() {
        num_rows += batch.num_rows();
    }
    // 100Mi - 50M = 54,857,600
    assert_eq!(num_rows, 54857600);
}

async fn warm_indexed_inequality_search(index: &BTreeIndex) {
    let result = index
        .search(
            &SargableQuery::Range(
                std::ops::Bound::Included(ScalarValue::UInt32(Some(50_000_000))),
                std::ops::Bound::Unbounded,
            ),
            &NoOpMetricsCollector,
        )
        .await
        .unwrap();
    let SearchResult::Exact(row_ids) = result else {
        panic!("Expected exact results")
    };

    // 100Mi - 50M = 54,857,600
    assert_eq!(row_ids.len(), Some(54857600));
}

async fn warm_indexed_isin_search(index: &BTreeIndex) {
    let result = index
        .search(
            &SargableQuery::IsIn(vec![
                ScalarValue::UInt32(Some(10000)),
                ScalarValue::UInt32(Some(50000000)),
                ScalarValue::UInt32(Some(150000000)), // Not found
                ScalarValue::UInt32(Some(287123)),
            ]),
            &NoOpMetricsCollector,
        )
        .await
        .unwrap();
    let SearchResult::Exact(row_ids) = result else {
        panic!("Expected exact results")
    };

    // Only 3 because 150M is not in dataset
    assert_eq!(row_ids.len(), Some(3));
}

fn bench_baseline(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();

    let fixture = rt.block_on(BenchmarkFixture::open());

    c.bench_function("baseline_equality", |b| {
        b.iter(|| rt.block_on(baseline_equality_search(&fixture)));
    });

    c.bench_function("baseline_inequality", |b| {
        b.iter(|| rt.block_on(baseline_inequality_search(&fixture)));
    });
}

fn bench_warm_indexed(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();
    let fixture = rt.block_on(BenchmarkFixture::open());

    let index = rt
        .block_on(BTreeIndex::load(fixture.index_store.clone(), None))
        .unwrap();

    c.bench_function("windexed_equality", |b| {
        b.iter(|| rt.block_on(warm_indexed_equality_search(index.as_ref())))
    });

    c.bench_function("windexed_inequality", |b| {
        b.iter(|| rt.block_on(warm_indexed_inequality_search(index.as_ref())))
    });

    c.bench_function("windexed_is_in", |b| {
        b.iter(|| rt.block_on(warm_indexed_isin_search(index.as_ref())))
    });
}

#[cfg(target_os = "linux")]
criterion_group!(
    name=benches;
    config = Criterion::default().significance_level(0.1).sample_size(10)
        .with_profiler(PProfProfiler::new(100, Output::Flamegraph(None)));
    targets = bench_baseline, bench_warm_indexed);

#[cfg(not(target_os = "linux"))]
criterion_group!(
    name=benches;
    config = Criterion::default().significance_level(0.1).sample_size(10);
    targets = bench_baseline, bench_warm_indexed);

criterion_main!(benches);
