[package]
name = "lance-linalg"
version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
repository = { workspace = true }
description = { workspace = true }
readme = "README.md"
keywords = { workspace = true }
categories = { workspace = true }

[dependencies]
arrow-array = { workspace = true }
arrow-buffer = { workspace = true }
arrow-ord = { workspace = true }
arrow-schema = { workspace = true }
bitvec = { workspace = true }
deepsize = { workspace = true }
futures = { workspace = true }
half = { workspace = true }
lance-arrow = { workspace = true }
lance-core = { workspace = true }
log = { workspace = true }
num-traits = { workspace = true }
rand = { workspace = true }
rayon = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }

[dev-dependencies]
approx = { workspace = true }
arrow-arith = { workspace = true }
criterion = { workspace = true }
lance-testing = { path = "../lance-testing" }
proptest.workspace = true

[build-dependencies]
cc = "1.0.83"

[features]
# Enable compiling multiple C kernels for fp16 SIMD computations.
# This requires GCC 12 / Clang 6 or later. (To get AVX-512 support,
# you need Clang 11 or later.)
fp16kernels = []

[target.'cfg(target_os = "linux")'.dev-dependencies]
pprof = { workspace = true }

[[bench]]
name = "l2"
harness = false

[[bench]]
name = "dot"
harness = false

[[bench]]
name = "argmin"
harness = false

[[bench]]
name = "cosine"
harness = false

[[bench]]
name = "hamming"
harness = false

[[bench]]
name = "norm_l2"
harness = false

[lints]
workspace = true
