[package]
name = "lance-arrow"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
readme = "README.md"
description = "Arrow Extension for Lance"
keywords.workspace = true
categories.workspace = true

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
arrow-array = { workspace = true }
arrow-buffer = { workspace = true }
arrow-data = { workspace = true }
arrow-cast = { workspace = true }
arrow-schema = { workspace = true }
arrow-select = { workspace = true }
bytes = { workspace = true }
half = { workspace = true }
num-traits = { workspace = true }
rand.workspace = true

[target.'cfg(target_arch = "wasm32")'.dependencies]
getrandom = { version = "0.2", features = ["js"] }

[lints]
workspace = true
