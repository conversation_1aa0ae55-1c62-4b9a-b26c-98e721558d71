name: Run benchmarks

on:
  workflow_dispatch:

jobs:
  dataset:
    timeout-minutes: 30
    runs-on: "ubuntu-22.04"
    strategy:
      matrix:
        dataset: ["sift"]
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        lfs: true
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    - uses: ./.github/workflows/build_linux_wheel
      with:
        python-minor-version: "10"
    - name: Pip install
      working-directory: python
      run: |
        pip install $(ls target/wheels/*.whl)
        pip install pandas
    - name: Run test
      working-directory: benchmarks/${{ matrix.dataset }}
      run: |
        ./test_dataset.sh
    - name: Archive results
      uses: actions/upload-artifact@v3
      with:
        name: ${{ matrix.dataset }}-results
        path: |
          benchmarks/${{ matrix.dataset }}/benchmark.csv