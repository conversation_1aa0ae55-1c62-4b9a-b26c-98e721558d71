# We create a composite action to be re-used both for testing and for releasing
name: build_wheel
description: "Build a lance wheel"
inputs:
  python-minor-version:
    description: "9, 10, 11, 12"
    required: true
  args:
    description: "--release"
    required: false
    default: ""
runs:
  using: "composite"
  steps:
    - name: Install macos dependency
      shell: bash
      run: |
        brew install protobuf
    - name: Build wheel
      uses: PyO3/maturin-action@v1
      with:
        command: build
        args: ${{ inputs.args }}
        working-directory: python
