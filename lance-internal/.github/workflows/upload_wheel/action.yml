name: upload-wheel

description: "Upload wheels to Pypi"
inputs:
  os:
    required: true
    description: "ubuntu-22.04 or macos-13"
  repo:
    required: false
    description: "pypi or testpypi"
    type: choice
    options:
      - "pypi"
      - "testpypi"
      - "fury"
    default: "pypi"
  pypi_token:
    required: true
    description: "release token for the repo"
  fury_token:
    required: true
    description: "release token for the fury repo"

runs:
  using: "composite"
  steps:
  - name: Install dependencies
    shell: bash
    run: |
      python -m pip install --upgrade pip
      pip install twine
  - name: Publish to PyPI
    working-directory: python
    shell: bash
    env:
      FURY_TOKEN: ${{ inputs.fury_token }}
      PYPI_TOKEN: ${{ inputs.pypi_token }}
    run: |
      if [ ${{ inputs.repo }} == "fury" ]; then
        WHEEL=$(ls target/wheels/pylance-*.whl 2> /dev/null | head -n 1)
        echo "Uploading $WHEEL to Fury"
        curl -f -F package=@$WHEEL https://$<EMAIL>/lancedb/
      else
        twine upload --repository ${{ inputs.repo }} \
          --username __token__ \
          --password $PYPI_TOKEN \
          target/wheels/pylance-*.whl
      fi
