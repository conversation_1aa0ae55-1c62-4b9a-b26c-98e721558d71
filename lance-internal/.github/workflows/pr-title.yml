name: PR Checks

on:
  # This needs to be a pull_request_target event to allow the workflow to
  # modify labels on the PR.
  pull_request_target:
    types: [opened, edited, synchronize, reopened]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  labeler:
    permissions:
      pull-requests: write
    name: Label PR
    runs-on: ubuntu-latest
    steps:
      - uses: srvaroa/labeler@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          fail_on_error: true
  breaking-change-checker:
    name: Validate breaking change policy
    runs-on: ubuntu-latest
    # Make sure we add any labels to this PR first.
    needs: labeler
    steps:
      # For security reasons, we check out the base branch to use its CI scripts.
      - uses: actions/checkout@v4
        with:
          path: base
      - uses: actions/checkout@v4
        with:
          # pull_request_target checks out the base branch by default, not
          # the PR branch.
          ref: "${{ github.event.pull_request.head.sha }}"
          path: pr
      - uses: actions/setup-python@v5
        with:
          python-version: "3.12"
      - run: pip install PyGithub
      - env:
          PR_NUMBER: ${{ github.event.pull_request.number }}
        working-directory: pr
        run: python ../base/ci/check_versions.py
  commitlint:
    permissions:
      pull-requests: write
    name: Verify PR title / description conforms to semantic-release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
      # These rules are disabled because Github will always ensure there
      # is a blank line between the title and the body and Github will
      # word wrap the description field to ensure a reasonable max line
      # length.
      - run: npm install @commitlint/config-conventional
      - run: >
          echo 'module.exports = {
            "rules": {
              "body-max-line-length": [0, "always", Infinity],
              "footer-max-line-length": [0, "always", Infinity],
              "body-leading-blank": [0, "always"]
            }
          }' > .commitlintrc.js
      - run: npx commitlint --extends @commitlint/config-conventional --verbose <<< $COMMIT_MSG
        env:
          COMMIT_MSG: >
            ${{ github.event.pull_request.title }}

            ${{ github.event.pull_request.body }}
      - if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            const message = `**ACTION NEEDED**
              Lance follows the [Conventional Commits specification](https://www.conventionalcommits.org/en/v1.0.0/) for release automation.

              The PR title and description are used as the merge commit message.\
              Please update your PR title and description to match the specification.

              For details on the error please inspect the "PR Title Check" action.
              `
            // Get list of current comments
            const comments = await github.paginate(github.rest.issues.listComments, {
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number
            });
            // Check if this job already commented
            for (const comment of comments) {
              if (comment.body === message) {
                return // Already commented
              }
            }
            // Post the comment about Conventional Commits
            github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: message
            })
            core.setFailed(message)
