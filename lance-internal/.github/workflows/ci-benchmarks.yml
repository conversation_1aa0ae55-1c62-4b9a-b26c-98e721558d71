name: Run Regression Benchmarks

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  bench_regress:
    timeout-minutes: 30
    runs-on: warp-custom-gcp-storage-benchmark
    env:
      # Need up-to-date compilers for kernels
      CC: clang-18
      CXX: clang++-18
    defaults:
      run:
        shell: bash
        working-directory: python
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
      - name: Authenticate with GCS
        uses: "google-github-actions/auth@v2"
        with:
          credentials_json: "${{ secrets.GCLOUD_BENCH_STORAGE_USER_KEY }}"
      - name: Install bencher
        uses: bencherdev/bencher@main
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11 # Ray does not support 3.12 yet.
      - uses: Swatinem/rust-cache@v2
        with:
          workspaces: python
      - name: Install dependencies
        run: |
          sudo apt update
          sudo apt install -y protobuf-compiler libssl-dev
      - name: Build
        run: |
          python -m venv venv
          source venv/bin/activate
          pip install maturin duckdb requests pytest pytest-benchmark
          maturin develop --locked --release
      - name: Generate datasets
        run: |
          python -m venv venv
          source venv/bin/activate
          python python/ci_benchmarks/datagen/gen_all.py
      - name: Run benchmarks
        run: |
          python -m venv venv
          source venv/bin/activate
          bencher run --project weston-lancedb --token ${{ secrets.LANCE_BENCHER_TOKEN }} --adapter python_pytest \
                      --branch main --testbed google-genoa --err --file results.json "python -mpytest --benchmark-json \
                      results.json python/ci_benchmarks"
