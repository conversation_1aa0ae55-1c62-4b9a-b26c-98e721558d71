name: Build and upload python wheels

on:
  release:
    types: [published]

jobs:
  linux:
    timeout-minutes: 60
    name: Python Linux 3.${{ matrix.python-minor-version }} ${{ matrix.config.platform }} manylinux${{ matrix.config.manylinux }}
    strategy:
      matrix:
        python-minor-version: [ "9" ]
        config:
          - platform: x86_64
            manylinux: "2_17"
            extra_args: ""
            runner: ubuntu-22.04
          - platform: x86_64
            manylinux: "2_28"
            extra_args: "--features fp16kernels"
            runner: ubuntu-22.04
          - platform: aarch64
            manylinux: "2_17"
            extra_args: ""
            runner: ubuntu-2404-4x-arm64
          - platform: aarch64
            manylinux: "2_28"
            extra_args: "--features fp16kernels"
            runner: ubuntu-2404-4x-arm64
    runs-on: ${{ matrix.config.runner }}
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        lfs: true
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.${{ matrix.python-minor-version }}
    - name: Handle tag
      id: handle_tag
      run: |
        # If the tag ends with -beta.N, we need to call setup_version.py 
        # and export repo as "fury" instead of "pypi"
        if [[ ${{ github.ref }} == refs/tags/*-beta.* ]]; then
          TAG=$(echo ${{ github.ref }} | sed 's/refs\/tags\///')
          pip install packaging
          python ci/setup_version.py $TAG
          echo "repo=fury" >> $GITHUB_OUTPUT
        else
          echo "repo=pypi" >> $GITHUB_OUTPUT
        fi
    - uses: ./.github/workflows/build_linux_wheel
      with:
        python-minor-version: ${{ matrix.python-minor-version }}
        args: "--release --strip ${{ matrix.config.extra_args }}"
        arm-build: ${{ matrix.config.platform == 'aarch64' }}
        manylinux: ${{ matrix.config.manylinux }}
    - uses: ./.github/workflows/upload_wheel
      with:
        pypi_token: ${{ secrets.PYPI_TOKEN }}
        fury_token: ${{ secrets.FURY_TOKEN }}
        repo: ${{ steps.handle_tag.outputs.repo }}
  mac:
    timeout-minutes: 60
    runs-on: ${{ matrix.config.runner }}
    strategy:
      matrix:
        python-minor-version: ["9"]
        config:
          - target: x86_64-apple-darwin
            runner: macos-13
          - target: aarch64-apple-darwin
            runner: macos-14
    env:
      MACOSX_DEPLOYMENT_TARGET: 10.15
    steps:
    - uses: actions/checkout@v4
      with:
        ref: ${{ inputs.ref }}
        fetch-depth: 0
        lfs: true
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.12
    - name: Handle tag
      id: handle_tag
      run: |
        # If the tag ends with -beta.N, we need to call setup_version.py 
        # and export repo as "fury" instead of "pypi"
        if [[ ${{ github.ref }} == refs/tags/*-beta.* ]]; then
          TAG=$(echo ${{ github.ref }} | sed 's/refs\/tags\///')
          pip install packaging
          python ci/setup_version.py $TAG
          echo "repo=fury" >> $GITHUB_OUTPUT
        else
          echo "repo=pypi" >> $GITHUB_OUTPUT
        fi
    - uses: ./.github/workflows/build_mac_wheel
      with:
        python-minor-version: ${{ matrix.python-minor-version }}
        args: "--release --strip --target ${{ matrix.config.target }} --features fp16kernels"
    - uses: ./.github/workflows/upload_wheel
      with:
        pypi_token: ${{ secrets.PYPI_TOKEN }}
        fury_token: ${{ secrets.FURY_TOKEN }}
        repo: ${{ steps.handle_tag.outputs.repo }}
  windows:
    timeout-minutes: 60
    runs-on: windows-latest
    strategy:
      matrix:
        python-minor-version: ["9"]
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
          fetch-depth: 0
          lfs: true
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: 3.${{ matrix.python-minor-version }}
      - name: Handle tag
        id: handle_tag
        shell: bash
        run: |
          # If the tag ends with -beta.N, we need to call setup_version.py 
          # and export repo as "fury" instead of "pypi"
          if [[ ${{ github.ref }} == refs/tags/*-beta.* ]]; then
            TAG=$(echo ${{ github.ref }} | sed 's/refs\/tags\///')
            pip install packaging
            python ci/setup_version.py $TAG
            echo "repo=fury" >> $GITHUB_OUTPUT
          else
            echo "repo=pypi" >> $GITHUB_OUTPUT
          fi
      - uses: ./.github/workflows/build_windows_wheel
        with:
          python-minor-version: ${{ matrix.python-minor-version }}
          args: "--release --strip"
          vcpkg_token: ${{ secrets.VCPKG_GITHUB_PACKAGES }}
      - uses: ./.github/workflows/upload_wheel
        with:
          pypi_token: ${{ secrets.PYPI_TOKEN }}
          fury_token: ${{ secrets.FURY_TOKEN }}
          repo: ${{ steps.handle_tag.outputs.repo }}
