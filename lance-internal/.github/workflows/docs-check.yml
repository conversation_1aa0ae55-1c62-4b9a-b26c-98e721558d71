name: Check docs

on:
  push:
    branches: ["main"]
  pull_request:
    paths:
      - docs/**
      - .github/workflows/docs-check.yml

env:
  RUSTFLAGS: "-C debuginfo=0"
  # according to: https://matklad.github.io/2021/09/04/fast-rust-builds.html
  # CI builds are faster with incremental disabled.
  CARGO_INCREMENTAL: "0"

jobs:
  # Single deploy job since we're just deploying
  check-docs:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.11
          cache: 'pip'
          cache-dependency-path: "docs/requirements.txt"
      - run: pip install -r docs/requirements.txt
      - name: Check links
        working-directory: docs
        run: |
          mkdocs-linkcheck src

