name: Publish Rust crate

on:
  release:
    # Use released instead of published, since we don't publish preview/beta
    # versions. Users instead install them from the git repo.
    types: [released]
  workflow_dispatch:
    inputs:
      tag:
        description: "Tag to publish (e.g., v1.0.0)"
        required: true
        type: string

env:
  # This env var is used by Swatinem/rust-cache@v2 for the cache
  # key, so we set it to make sure it is always consistent.
  CARGO_TERM_COLOR: always
  CARGO_INCREMENTAL: "0"
  RUSTFLAGS: "-C debuginfo=0"

jobs:
  build:
    # Needs additional disk space for the full build.
    runs-on: ubuntu-2404-8x-x64
    timeout-minutes: 60
    env:
      # Need up-to-date compilers for kernels
      CC: clang-18
      CXX: clang++-18
    defaults:
      run:
        working-directory: .
    steps:
      - uses: actions/checkout@v4
      - uses: Swatinem/rust-cache@v2
        with:
          workspaces: rust
      - name: Verify and checkout specified tag
        if: github.event_name == 'workflow_dispatch'
        run: |
          git fetch --all --tags
          if git rev-parse ${{ github.event.inputs.tag }} >/dev/null 2>&1; then
            git checkout ${{ github.event.inputs.tag }}
            echo "Successfully checked out tag ${{ github.event.inputs.tag }}"
          else
            echo "Error: Tag ${{ github.event.inputs.tag }} does not exist"
            echo "Available tags:"
            git tag -l
            exit 1
          fi
      - name: Install dependencies
        run: |
          sudo apt update
          sudo apt install -y protobuf-compiler libssl-dev
      - uses: albertlockett/publish-crates@v2.2
        with:
          registry-token: ${{ secrets.CARGO_REGISTRY_TOKEN }}
          args: "--all-features"
          path: .
