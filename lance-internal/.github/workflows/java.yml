name: Build and Run Java JNI Tests

on:
  push:
    branches:
      - main
  pull_request:
    paths:
      - java/**
      - rust/**
      - .github/workflows/java.yml

env:
  # This env var is used by Swatinem/rust-cache@v2 for the cache
  # key, so we set it to make sure it is always consistent.
  CARGO_TERM_COLOR: always
  # Disable full debug symbol generation to speed up CI build and keep memory down
  # "1" means line tables only, which is useful for panic tracebacks.
  RUSTFLAGS: "-C debuginfo=1"
  RUST_BACKTRACE: "1"
  # according to: https://matklad.github.io/2021/09/04/fast-rust-builds.html
  # CI builds are faster with incremental disabled.
  CARGO_INCREMENTAL: "0"
  CARGO_BUILD_JOBS: "1"

jobs:
  rust-clippy-fmt:
    runs-on: ubuntu-24.04
    name: Rust Clippy and Fmt Check
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - uses: Swatinem/rust-cache@v2
        with:
          workspaces: |
            lance
            java/core/lance-jni -> ../target/rust-maven-plugin/lance-jni
      - name: Install dependencies
        run: |
          sudo apt update
          sudo apt install -y protobuf-compiler libssl-dev
      # pin the toolchain version to avoid surprises
      - uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          components: clippy, rustfmt
      - uses: rui314/setup-mold@v1
      - name: Install cargo-llvm-cov
        uses: taiki-e/install-action@cargo-llvm-cov
      - name: Run cargo fmt
        working-directory: java/core/lance-jni
        run: cargo fmt --check
      - name: Rust Clippy
        working-directory: java/core/lance-jni
        run: cargo clippy --all-targets -- -D warnings

  build-and-test-java:
    runs-on: ubuntu-24.04
    timeout-minutes: 60
    strategy:
      matrix:
        java-version: [8, 11, 17]
    name: Build and Test with Java ${{ matrix.java-version }}
    steps:
      - name: Install dependencies
        run: |
          sudo apt update
          sudo apt install -y protobuf-compiler libssl-dev
      # pin the toolchain version to avoid surprises
      - uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          toolchain: stable
      - uses: rui314/setup-mold@v1
      - name: Install cargo-llvm-cov
        uses: taiki-e/install-action@cargo-llvm-cov
      - name: Checkout repository
        uses: actions/checkout@v4
      - uses: Swatinem/rust-cache@v2
        with:
          workspaces: java/core/lance-jni -> ../target/rust-maven-plugin/lance-jni
      - name: Set up Java ${{ matrix.java-version }}
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: ${{ matrix.java-version }}
          cache: "maven"
      - name: Running code style check with Java ${{ matrix.java-version }}
        working-directory: java
        run: |
          mvn spotless:check
      - name: Running tests with Java ${{ matrix.java-version }}
        working-directory: java
        run: |
          mvn install
