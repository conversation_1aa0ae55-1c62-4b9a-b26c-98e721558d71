name: Create release commit

on:
  workflow_dispatch:
    inputs:
      dry_run:
        description: 'Dry run (create the local commit/tags but do not push it)'
        required: true
        default: false
        type: boolean
      type:
        description: 'What kind of release is this?'
        required: true
        default: 'preview'
        type: choice
        options:
          - preview
          - stable
      draft-release:
        description: 'Create a draft release on GitHub'
        required: true
        default: false
        type: boolean

jobs:
  bump-version:
    runs-on: ubuntu-latest
    steps:
    - name: Output Inputs
      run: echo "${{ toJSON(github.event.inputs) }}"
    - name: Check out main
      uses: actions/checkout@v4
      with:
        ref: ${{ github.ref }}
        persist-credentials: false
        fetch-depth: 0
        lfs: true
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Create tag
      id: create_tag
      working-directory: rust
      env:
        RELEASE_TYPE: ${{ inputs.type }}
      run: |
        # Get the current version from cargo metadata
        CURRENT_VERSION=$(cargo metadata --no-deps --format-version 1 | jq '.packages[0].version' | xargs echo)

        if [ "$RELEASE_TYPE" == "stable" ]; then
          echo "Creating stable release for version $CURRENT_VERSION"
          TAG="v${CURRENT_VERSION}"
        else
          # Get a list of all tags, filter for current version beta tags, sort them and get the last one
          LAST_BETA_TAG=$(git tag | grep "^v${CURRENT_VERSION}-beta." | sort -V | tail -n 1)

          if [ -z "$LAST_BETA_TAG" ]; then
            # If there are no existing beta tags for the current version, start with beta.1
            NEXT_BETA_TAG="v${CURRENT_VERSION}-beta.1"
          else
            # If there are existing beta tags, increment the last beta number to get the next one
            LAST_BETA_NUMBER=$(echo $LAST_BETA_TAG | sed "s/v${CURRENT_VERSION}-beta.//")
            NEXT_BETA_NUMBER=$((LAST_BETA_NUMBER + 1))
            NEXT_BETA_TAG="v${CURRENT_VERSION}-beta.${NEXT_BETA_NUMBER}"
          fi

          echo "Creating beta release for version $CURRENT_VERSION: $NEXT_BETA_TAG"
          TAG=$NEXT_BETA_TAG
        fi

        git tag $TAG
        echo "tag=$TAG" >> $GITHUB_OUTPUT
    - name: Push new version tag
      if: ${{ !inputs.dry_run }}
      uses: ad-m/github-push-action@master
      with:
        github_token: ${{ secrets.LANCE_RELEASE_TOKEN }}
        branch: ${{ github.ref }}
        tags: true
    - name: Create GH release
      if: ${{ !inputs.dry_run }}
      uses: softprops/action-gh-release@v2
      with:
        prerelease: ${{ inputs.type == 'preview' }}
        draft: ${{ inputs.draft-release }}
        tag_name: ${{ steps.create_tag.outputs.tag }}
        token: ${{ secrets.LANCE_RELEASE_TOKEN }}
        generate_release_notes: true

    # Remaining steps only apply to stable releases
    - name: Call bumpversion
      uses: ./.github/workflows/bump-version
      if: inputs.type == 'stable'
      with:
        part: patch
    - name: Commit bump version
      if: inputs.type == 'stable'
      run: |
        git add -u
        git commit -m "Bump version"
    - name: Push new version 
      if: ${{ inputs.type == 'stable' && !inputs.dry_run }}
      uses: ad-m/github-push-action@master
      with:
        github_token: ${{ secrets.LANCE_RELEASE_TOKEN }}
        branch: ${{ github.ref }}