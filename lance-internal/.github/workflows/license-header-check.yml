name: Check license headers
on:
  push:
    branches:
      - main
  pull_request:
    paths:
      - rust/**
      - python/**
      - protos/**
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Install license-header-checker
        run: curl -s https://raw.githubusercontent.com/lluissm/license-header-checker/master/install.sh | bash
      - name: Check license headers (rust)
        run: ./bin/license-header-checker -a -v ./rust/license_header.txt rust rs && [[ -z `git status -s` ]]
      - name: Check license headers (python)
        run: ./bin/license-header-checker -a -v ./python/license_header.txt python py && [[ -z `git status -s` ]]
      - name: Check license headers (protos)
        run: ./bin/license-header-checker -a -v ./protos/license_header.txt protos proto && [[ -z `git status -s` ]]
