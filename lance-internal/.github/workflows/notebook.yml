name: Run notebook

on:
  push:
    branches:
      - main
  pull_request:
    paths:
      - python/**
      - rust/**
      - protos/**
      - notebooks/**

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  linux:
    timeout-minutes: 30
    runs-on: "ubuntu-22.04"
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        lfs: true
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    - uses: ./.github/workflows/build_linux_wheel
      with:
        python-minor-version: "10"
    - name: Pip install
      working-directory: python
      shell: bash
      run: |
        pip3 install jupyter duckdb>=0.7
        pip3 install $(ls target/wheels/*.whl)
    - name: Run python tests
      shell: bash
      working-directory: notebooks
      run: |
        jupyter nbconvert --to notebook --execute quickstart.ipynb
