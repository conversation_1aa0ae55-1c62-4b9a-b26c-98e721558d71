name: Python

on:
  push:
    branches:
      - main
  pull_request:
    paths:
      - Cargo.*
      - python/**
      - rust/**
      - protos/**
      - .github/workflows/python.yml
      - .github/workflows/build_linux_wheel/**
      - .github/workflows/build_mac_wheel/**
      - .github/workflows/run_tests/**

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  # This env var is used by Swatinem/rust-cache@v2 for the cache
  # key, so we set it to make sure it is always consistent.
  CARGO_TERM_COLOR: always
  # Disable full debug symbol generation to speed up CI build and keep memory down
  # "1" means line tables only, which is useful for panic tracebacks.
  RUSTFLAGS: "-C debuginfo=line-tables-only"
  RUST_BACKTRACE: "1"
  CI: "true"
  # Color output for pytest is off by default.
  PYTEST_ADDOPTS: "--color=yes"
  FORCE_COLOR: "1"
  # Change this to bust all caches (may be needed periodically if the caches accumulate
  # a lot of cruft).
  CACHE_PREFIX: "1"

jobs:
  lint:
    timeout-minutes: 45
    runs-on: "ubuntu-24.04"
    defaults:
      run:
        shell: bash
        working-directory: python
    env:
      # Need up-to-date compilers for kernels
      CC: clang-18
      CXX: clang++-18
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11 # Ray does not support 3.12 yet.
      - uses: Swatinem/rust-cache@v2
        with:
          workspaces: python
          prefix-key: ${{ env.CACHE_PREFIX }}
      - name: Install linting tools
        run: |
          pip install ruff==0.11.2 maturin tensorflow tqdm ray[data] pyright datasets polars[pyarrow,pandas]
          pip install torch --index-url https://download.pytorch.org/whl/cpu
      - name: Lint Python
        run: |
          ruff format --check --diff python
          ruff check python
          pyright
      - name: Install dependencies
        run: |
          sudo apt update
          sudo apt install -y protobuf-compiler libssl-dev
      - name: Install rustfmt
        run: rustup component add rustfmt
      - name: Install clippy
        run: rustup component add clippy
      - name: Lint Rust
        run: |
          ALL_FEATURES=`cargo metadata --format-version=1 --no-deps | jq -r '.packages[] | .features | keys | .[]' | grep -v protoc | sort | uniq | paste -s -d "," -`
          cargo fmt --all -- --check
          cargo clippy --locked --features ${ALL_FEATURES} --tests -- -D warnings
      - name: Build
        run: |
          python -m venv venv
          source venv/bin/activate
          pip install torch tqdm --index-url https://download.pytorch.org/whl/cpu
          pip install maturin
          maturin develop --locked --extras tests,ray
      - name: Run doctest
        run: |
          source venv/bin/activate
          pytest --doctest-modules python/lance
  linux:
    timeout-minutes: 45
    strategy:
      matrix:
        python-minor-version: ["9", "12"]
    name: "Python Linux 3.${{ matrix.python-minor-version }} x86_64"
    runs-on: "ubuntu-24.04"
    defaults:
      run:
        shell: bash
        working-directory: python
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.${{ matrix.python-minor-version }}
      - uses: Swatinem/rust-cache@v2
        with:
          workspaces: python
          prefix-key: ${{ env.CACHE_PREFIX }}
      - uses: ./.github/workflows/build_linux_wheel
      - uses: ./.github/workflows/run_tests
      - name: Generate forward compatibility files
        run: python python/tests/forward_compat/datagen.py
      - name: Run forward compatibility tests (pylance 0.16.0)
        run: |
          python -m venv venv
          source venv/bin/activate
          pip install pytest pylance==0.16.0
          pytest python/tests/forward_compat --run-forward
      - name: Run forward compatibility tests (pylance 0.29.1.beta2)
        run: |
          source venv/bin/activate
          pip install pytest --pre --extra-index-url https://pypi.fury.io/lancedb/ pylance==0.29.1.beta2
          pytest python/tests/forward_compat --run-forward
      # Make sure wheels are not included in the Rust cache
      - name: Delete wheels
        run: sudo rm -rf target/wheels
  linux-arm:
    timeout-minutes: 45
    runs-on: ubuntu-2404-4x-arm64
    name: Python Linux 3.12 ARM
    defaults:
      run:
        shell: bash
        working-directory: python
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.12
      - uses: Swatinem/rust-cache@v2
        with:
          workspaces: python
          prefix-key: ${{ env.CACHE_PREFIX }}
      - uses: ./.github/workflows/build_linux_wheel
        with:
          arm-build: "true"
          manylinux: "2_28"
      - name: Install dependencies
        run: |
          sudo apt update -y -qq
          sudo apt install -y libhdf5-dev
      - uses: ./.github/workflows/run_tests
      # Make sure wheels are not included in the Rust cache
      - name: Delete wheels
        run: sudo rm -rf target/wheels
  mac:
    timeout-minutes: 45
    name: Python macOS 3.12 ARM
    runs-on: "macos-14"
    defaults:
      run:
        shell: bash
        working-directory: python
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
      - uses: Swatinem/rust-cache@v2
        with:
          workspaces: python
          prefix-key: ${{ env.CACHE_PREFIX }}
      - uses: ./.github/workflows/build_mac_wheel
      - uses: ./.github/workflows/run_tests
        with:
          skip-torch: "true"
      # Make sure wheels are not included in the Rust cache
      - name: Delete wheels
        run: rm -rf target/wheels
  windows:
    runs-on: windows-latest
    timeout-minutes: 90
    defaults:
      run:
        shell: powershell
        working-directory: python
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
      - uses: Swatinem/rust-cache@v2
        with:
          workspaces: python
          prefix-key: ${{ env.CACHE_PREFIX }}
      - uses: ./.github/workflows/build_windows_wheel
      - uses: ./.github/workflows/run_tests
  aws-integtest:
    timeout-minutes: 45
    runs-on: "ubuntu-latest"
    defaults:
      run:
        shell: bash
        working-directory: python
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11" # TODO: upgrade when ray supports 3.12
      - uses: Swatinem/rust-cache@v2
        with:
          workspaces: python
          prefix-key: ${{ env.CACHE_PREFIX }}
      - uses: ./.github/workflows/build_linux_wheel
      - name: Install dependencies
        run: |
          pip install ray[data]
          pip install torch --index-url https://download.pytorch.org/whl/cpu
      - uses: ./.github/workflows/run_integtests
      # Make sure wheels are not included in the Rust cache
      - name: Delete wheels
        run: sudo rm -rf target/wheels
