# We create a composite action to be re-used both for testing and for releasing
name: build-linux-wheel
description: "Build a manylinux wheel for lance"
inputs:
  python-minor-version:
    description: "9, 10, 11, 12"
    required: true
  args:
    description: "--release"
    required: false
    default: ""
  arm-build:
    description: "Build for arm64 instead of x86_64"
    # Note: this does *not* mean the host is arm64, since we might be cross-compiling.
    required: false
    default: "false"
  manylinux:
    description: "The manylinux version to build for"
    required: false
    default: "2_17"
runs:
  using: "composite"
  steps:
    - name: CONFIRM ARM BUILD
      shell: bash
      run: |
        echo "ARM BUILD: ${{ inputs.arm-build }}"
    - name: Build x86_64 Manylinux2014 wheel
      if: ${{ inputs.arm-build == 'false' && inputs.manylinux == '2_17' }}
      uses: PyO3/maturin-action@v1
      with:
        command: build
        working-directory: python
        target: x86_64-unknown-linux-gnu
        manylinux: ${{ inputs.manylinux }}
        args: ${{ inputs.args }}
        before-script-linux: |
          set -e
          yum install -y openssl-devel \
            && curl -L https://github.com/protocolbuffers/protobuf/releases/download/v24.4/protoc-24.4-linux-$(uname -m).zip > /tmp/protoc.zip \
            && unzip /tmp/protoc.zip -d /usr/local \
            && rm /tmp/protoc.zip
    - name: Build x86_64 Manylinux {manylinux} wheel
      if: ${{ inputs.arm-build == 'false' && inputs.manylinux != '2_17' }}
      uses: PyO3/maturin-action@v1
      with:
        command: build
        working-directory: python
        target: x86_64-unknown-linux-gnu
        manylinux: ${{ inputs.manylinux }}
        docker-options: |
          -e CC=clang
          -e CXX=clang++
        args: ${{ inputs.args }}
        before-script-linux: |
          set -e
          yum install -y openssl-devel clang \
            && curl -L https://github.com/protocolbuffers/protobuf/releases/download/v24.4/protoc-24.4-linux-$(uname -m).zip > /tmp/protoc.zip \
            && unzip /tmp/protoc.zip -d /usr/local \
            && rm /tmp/protoc.zip
    - name: Build Arm Manylinux Wheel
      if: ${{ inputs.arm-build == 'true' }}
      uses: PyO3/maturin-action@v1
      with:
        command: build
        working-directory: python
        target: aarch64-unknown-linux-gnu
        manylinux: ${{ inputs.manylinux }}
        args: ${{ inputs.args }}
        before-script-linux: |
          set -e
          yum install -y openssl-devel clang \
            && curl -L https://github.com/protocolbuffers/protobuf/releases/download/v24.4/protoc-24.4-linux-aarch_64.zip > /tmp/protoc.zip \
            && unzip /tmp/protoc.zip -d /usr/local \
            && rm /tmp/protoc.zip
