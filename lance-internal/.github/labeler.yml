version: 1
appendOnly: true
# Labels are applied based on conventional commits standard
# https://www.conventionalcommits.org/en/v1.0.0/
# These labels are later used in release notes. See .github/release.yml
labels:
# If the PR title has an ! before the : it will be considered a breaking change
# For example, `feat!: add new feature` will be considered a breaking change
- label: breaking-change
  title: "^[^:]+!:.*"
- label: breaking-change
  body: "BREAKING CHANGE"
- label: enhancement
  title: "^feat(\\(.+\\))?!?:.*"
- label: bug
  title: "^fix(\\(.+\\))?!?:.*"
- label: documentation
  title: "^docs(\\(.+\\))?!?:.*"
- label: performance
  title: "^perf(\\(.+\\))?!?:.*"
- label: ci
  title: "^ci(\\(.+\\))?!?:.*"
- label: chore
  title: "^(chore|test|build|style)(\\(.+\\))?!?:.*"
- label: java
  files:
    - "java\\/.*"
- label: python
  files:
    - "python\\/.*"
