[profile.release]
lto = "thin"
codegen-units = 1

[profile.release-with-debug]
inherits = "release"
debug = true
# Prioritize compile time over runtime performance
codegen-units = 16
lto = "thin"

[target.'cfg(all())']
rustflags = [
    "-Wclippy::all",
    "-Wclippy::fallible_impl_from",
    "-Wclippy::manual_let_else",
    "-Wclippy::string_add_assign",
    "-Wclippy::string_add",
    "-Wclippy::string_lit_as_bytes",
    "-Wclippy::string_to_string",
    "-Wclippy::use_self",
    "-Aclippy::redundant_pub_crate", # PyO3 macros don't pass this.
]

[target.x86_64-unknown-linux-gnu]
rustflags = ["-C", "target-cpu=haswell", "-C", "target-feature=+avx2,+fma,+f16c"]

[target.aarch64-apple-darwin]
rustflags = ["-C", "target-cpu=apple-m1", "-C", "target-feature=+neon,+fp16"]
