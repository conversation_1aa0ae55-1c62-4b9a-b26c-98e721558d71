# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright The Lance Authors

from typing import Any, Dict, List

import pyarrow as pa

class LanceField:
    def name(self) -> str: ...
    def id(self) -> int: ...
    def children(self) -> List[LanceField]: ...

class LanceSchema:
    def fields(self) -> List[LanceField]: ...
    def to_pyarrow(self) -> pa.Schema: ...
    @staticmethod
    def from_pyarrow(schema: pa.Schema) -> "LanceSchema": ...

def schema_to_json(schema: pa.Schema) -> Dict[str, Any]: ...
def json_to_schema(schema_json: Dict[str, Any]) -> pa.Schema: ...
