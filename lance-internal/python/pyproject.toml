[project]
name = "pylance"
dynamic = ["version"]
dependencies = ["pyarrow>=14", "numpy>=1.22"]
description = "python wrapper for Lance columnar format"
authors = [{ name = "Lance Devs", email = "<EMAIL>" }]
license = { file = "LICENSE" }
repository = "https://github.com/lancedb/lance"
readme = "README.md"
requires-python = ">=3.9"
keywords = [
    "data-format",
    "data-science",
    "machine-learning",
    "arrow",
    "data-analytics",
]
categories = [
    "database-implementations",
    "data-structures",
    "development-tools",
    "science",
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Environment :: Console",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Rust",
    "Topic :: Scientific/Engineering",
]

[tool.maturin]
python-source = "python"

[build-system]
requires = ["maturin>=1.4"]
build-backend = "maturin"

[project.optional-dependencies]
tests = [
    "boto3",
    "datasets",
    "duckdb",
    "ml_dtypes",
    "pillow",
    "pandas",
    "polars[pyarrow,pandas]",
    "pytest",
    "tensorflow",
    "tqdm",
    "datafusion",
]
dev = ["ruff==0.4.1", "pyright"]
benchmarks = ["pytest-benchmark"]
torch = ["torch"]
ray = ["ray[data]<2.38; python_version<'3.12'"]

[tool.ruff]
lint.select = ["F", "E", "W", "I", "G", "TCH", "PERF", "B019"]

[tool.ruff.lint.per-file-ignores]
"*.pyi" = ["E301", "E302"]

[tool.pyright]
pythonVersion = "3.12"
# TODO: expand this list as we fix more files.
include = [
    "python/lance/util.py",
    "python/lance/debug.py",
    "python/lance/tracing.py",
    "python/lance/dependencies.py",
    "python/lance/schema.py",
    "python/lance/file.py",
    "python/lance/util.py",
]
# Dependencies like pyarrow make this difficult to enforce strictly.
reportMissingTypeStubs = "warning"
reportImportCycles = "error"
reportUnusedImport = "error"
reportPropertyTypeMismatch = "error"
reportUnnecessaryCast = "error"


[tool.pytest.ini_options]
markers = [
    "cuda: tests which rely on having a CUDA-capable GPU",
    "integration: mark test to run only on named environment",
    "gpu: tests which rely on pytorch and some kind of gpu",
    "slow",
    "torch: tests which rely on pytorch being installed",
]
filterwarnings = [
    'error::FutureWarning',
    'error::DeprecationWarning',
    # Boto3
    'ignore:.*datetime\.datetime\.utcnow\(\) is deprecated.*:DeprecationWarning',
    # Pandas 2.2 on Python 2.12
    'ignore:.*datetime\.datetime\.utcfromtimestamp\(\) is deprecated.*:DeprecationWarning',
    # Pytorch 2.2 on Python 2.12
    'ignore:.*is deprecated and will be removed in Python 3\.14.*:DeprecationWarning',
    'ignore:.*The distutils package is deprecated.*:DeprecationWarning',
]
