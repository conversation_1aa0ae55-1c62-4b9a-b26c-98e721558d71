// Copyright 2023 Lance Developers.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

use pyo3::{pyclass, pymethods};

#[pyclass(get_all)]
#[derive(Debug)]
pub struct CleanupStats {
    pub bytes_removed: u64,
    pub old_versions: u64,
}

#[pymethods]
impl CleanupStats {
    fn __repr__(&self) -> String {
        format!("{self:?}")
    }
}
